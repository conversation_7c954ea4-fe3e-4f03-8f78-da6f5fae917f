package websocket

import (
	"encoding/json"
	"fmt"
	"net/url"
	"sase-client-info/internal/common"
	"sase-client-info/internal/common/errors"
	"sase-client-info/internal/config"
	"sase-client-info/internal/dto"
	"sase-client-info/pkg/logger"
	"sase-client-info/pkg/utils/helper"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

// Handler 策略处理器
type Handler struct{}

// NewHandler 创建策略处理器
func NewHandler() *Handler {
	return &Handler{}
}

// GetAddr 获取websocket地址
func (h *Handler) GetAddr(c *gin.Context) {

	orgName := c.GetString("product_id")
	if config.Get().DevTest.Switch {
		orgName = config.Get().DevTest.EdrProductId
	}

	requestUrl := strings.TrimSuffix(config.Get().Passport.PassportBackendUrl, "/") + "/external/get_org_app"
	currentTime := cast.ToString(time.Now().Unix())
	appid := config.Get().Passport.Appid
	appSecret := config.Get().Passport.AppSecret

	uuId := cast.ToString(time.Now().UnixNano())
	sign := helper.GeneratorPassportSign(appid, appSecret, uuId, currentTime)

	origin := c.GetHeader("Origin")
	if origin == "" {
		origin = c.GetHeader("Referer")
	}
	logger.Info("origin:", origin)
	originInfo, err := url.Parse(origin)
	if err != nil {
		logger.Errorf("error from passport get jump url parse origin: %v,%v", err, origin)
		common.ResponseResult(c, errors.PassportGetUserError)
		return
	}

	originHost := originInfo.Scheme + "://" + originInfo.Host

	requestData := map[string]interface{}{
		"org_name": orgName,
		"sign":     sign,
		"appid":    cast.ToInt(appid),
		"time":     cast.ToInt(currentTime),
		"uuid":     uuId,
		"callback": originHost,
	}

	jsonStr, _ := json.Marshal(requestData)

	respByte, err := helper.CurlRequest(requestUrl, "POST", jsonStr, map[string]string{}, config.Get().Settings.InsecureSkipVerify)
	if err != nil {
		logger.Errorf("error from service verify token request get_user: %v %v %v", err, requestUrl, string(jsonStr))
		common.ResponseResult(c, errors.PassportGetUserError)
		return
	}

	logger.Info("get passport user request data:", string(jsonStr), " response data:", string(respByte))

	var res = &dto.PassportResponse{}

	err = json.Unmarshal(respByte, &res)

	if res == nil || err != nil {
		logger.Errorf("error from service verify token unmarshal json: %v %v", err, string(respByte))
		common.ResponseResult(c, errors.PassportGetUserError)
		return
	}

	dataMap := cast.ToStringMap(res.Data)

	addrEncrypt := cast.ToString(dataMap["addr"])
	addr, _ := helper.AesDecrypt(addrEncrypt, []byte(config.Get().Passport.AppSecret))
	fmt.Println(string(addr))
	data := map[string]interface{}{
		"addr": string(addr),
	}

	common.ResponseResult(c, errors.Success, data)
}
