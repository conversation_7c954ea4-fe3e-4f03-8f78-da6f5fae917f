package department_role_relation

import (
	"sase-client-info/internal/common"
	"sase-client-info/internal/common/errors"
	"sase-client-info/internal/dto"

	"github.com/gin-gonic/gin"
)

// Handler 部门角色关系处理器
type Handler struct {
}

// NewHandler 创建新的部门角色关系处理器
func NewHandler() *Handler {
	return &Handler{}
}

// CreateDepartmentRoleRelation 创建部门角色的人员关系
func (h *Handler) CreateDepartmentRoleRelation(c *gin.Context) {
	common.ResponseResult(c, errors.Success, nil)
}

// CanSelectMembers 查询可以选中的成员
func (h *Handler) CanSelectMembers(c *gin.Context) {
	response := &dto.SelectDepartmentMemberResponse{
		Total: 1,
		Items: []dto.SimpleDepartmentMemberResponse{
			{
				UserCode: "USER002",
				UserName: "李四",
				DeptCode: "DEPT001",
				DeptPath: []string{"DEPT000", "DEPT001"},
				RoleCode: "ROLE001",
			},
		},
	}
	common.ResponseResult(c, errors.Success, response)
}

// SelectedMembers 查询选中的成员
func (h *Handler) SelectedMembers(c *gin.Context) {
	response := &dto.SelectDepartmentMemberResponse{
		Total: 1,
		Items: []dto.SimpleDepartmentMemberResponse{
			{
				UserCode: "USER002",
				UserName: "李四",
				DeptCode: "DEPT001",
				DeptPath: []string{"DEPT000", "DEPT001"},
				RoleCode: "",
			},
		},
	}
	common.ResponseResult(c, errors.Success, response)
}
