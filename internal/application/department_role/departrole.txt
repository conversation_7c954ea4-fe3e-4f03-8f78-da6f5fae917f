// 创建部门角色请求
POST /api/v1/org/departments/roles
Request:
{
    "role_name": "应用管理员"
}

Response:
{
    "code": 200,
    "message": "success",
    "data": {
        "role_code": "ROLE001",
        "role_name": "应用管理员"
    }
}

// 更新部门角色请求
PUT /api/v1/org/departments/roles/{role_code}
Request:
{
    "role_name": "系统管理员"
}

Response:
{
    "code": 200,
    "message": "success",
    "data": {
        "role_code": "ROLE001",
        "role_name": "系统管理员"
    }
}

// 删除部门角色请求
DELETE /api/v1/org/departments/roles/{role_code}
Response:
{
    "code": 200,
    "message": "success",
    "data": null
}

// 查询部门角色列表请求
POST /api/v1/org/departments/roles/search
Request:
{
    "page": 1,
    "limit": 20
}

Response:
{
    "code": 200,
    "message": "success",
    "data": {
        "total": 2,
        "items": [
            {
                "role_code": "ROLE001",
                "role_name": "应用管理员",
                "create_at": 1710921600,
                "update_at": 1710925200
            },
            {
                "role_code": "ROLE002",
                "role_name": "业务管理员",
                "create_at": 1710921600,
                "update_at": 1710925200
            }
        ]
    }
}

// 查询单个部门角色详情请求
GET /api/v1/org/departments/roles/{role_code}
Response:
{
    "code": 200,
    "message": "success",
    "data": {
        "role_code": "ROLE001",
        "role_name": "应用管理员"
    }
}
