package department_role

import (
	"sase-client-info/internal/common"
	"sase-client-info/internal/common/errors"
	"sase-client-info/internal/dto"

	"github.com/gin-gonic/gin"
)

// Handler 部门角色处理器
type Handler struct {
}

// NewHandler 创建新的部门角色处理器
func NewHandler() *Handler {
	return &Handler{}
}

// CreateDepartmentRole 创建部门角色
func (h *Handler) CreateDepartmentRole(c *gin.Context) {
	response := &dto.DepartmentRoleResponse{
		RoleCode: "ROLE001",
		RoleName: "应用管理员",
	}
	common.ResponseResult(c, errors.Success, response)
}

// UpdateDepartmentRole 更新部门角色
func (h *Handler) UpdateDepartmentRole(c *gin.Context) {
	response := &dto.DepartmentRoleResponse{
		RoleCode: "ROLE001",
		RoleName: "系统管理员",
	}
	common.ResponseResult(c, errors.Success, response)
}

// DeleteDepartmentRole 删除部门角色
func (h *Handler) DeleteDepartmentRole(c *gin.Context) {
	common.ResponseResult(c, errors.Success, nil)
}

// SearchDepartmentRoles 查询部门角色列表
func (h *Handler) SearchDepartmentRoles(c *gin.Context) {
	response := &dto.DepartmentRoleListResponse{
		Total: 2,
		Items: []dto.DepartmentRoleResponse{
			{
				RoleCode: "ROLE001",
				RoleName: "应用管理员",
				CreateAt: 1710921600,
				UpdateAt: 1710925200,
			},
			{
				RoleCode: "ROLE002",
				RoleName: "业务管理员",
				CreateAt: 1710921600,
				UpdateAt: 1710925200,
			},
		},
	}
	common.ResponseResult(c, errors.Success, response)
}

// GetDepartmentRoleDetail 查询部门角色详情
func (h *Handler) GetDepartmentRoleDetail(c *gin.Context) {
	response := &dto.DepartmentRoleResponse{
		RoleCode: "ROLE001",
		RoleName: "应用管理员",
	}
	common.ResponseResult(c, errors.Success, response)
}
