// 创建组织请求
POST /api/v1/org/platforms/configs
Request:
{
    "org_name": "总公司",
    "source_type": "DINGTALK",
    "source_config": {}
}

Response:
{
    "code": 200,
    "message": "success",
    "data": {
        "org_code": "ORG001",
        "org_name": "总公司",
        "source_type": "DINGTALK",
        "source_config": {},
        "product_id": "PROD001",
        "sync_status": "NOT_SYNCED",
        "last_sync_time": null,
        "last_update_batch_no": "BATCH20240325001",
        "status": 1
    }
}

// 更新组织请求
PUT /api/v1/org/platforms/configs/{org_code}
Request:
{
    "org_name": "总公司-新",
    "source_type": "DINGTALK",
    "source_config": {
        "app_key": "xxx",
        "app_secret": "xxx"
    }
}

Response:
{
    "code": 200,
    "message": "success",
    "data": {
        "org_code": "ORG001",
        "org_name": "总公司-新",
        "source_type": "DINGTALK",
        "source_config": {
            "app_key": "xxx",
            "app_secret": "xxx"
        },
        "product_id": "PROD001",
        "sync_status": "NOT_SYNCED",
        "last_sync_time": null,
        "last_update_batch_no": "BATCH20240325001",
        "status": 1
    }
}

// 删除组织请求
DELETE /api/v1/org/platforms/configs/{org_code}
Response:
{
    "code": 200,
    "message": "success",
    "data": null
}

// 查询组织列表请求
POST /api/v1/org/platforms/configs/search
Request:
{
    "org_name": "总公司",
    "sync_status": "NOT_SYNCED",
    "page": 1,
    "limit": 20
}

Response:
{
    "code": 200,
    "message": "success",
    "data": {
        "total": 1,
        "items": [
            {
                "org_code": "ORG001",
                "org_name": "总公司",
                "source_type": "DINGTALK",
                "source_config": {},
                "product_id": "PROD001",
                "sync_status": "NOT_SYNCED",
                "last_sync_time": null,
                "last_update_batch_no": "BATCH20240325001",
                "status": 1
            }
        ]
    }
}

// 查询单个组织详情请求
GET /api/v1/org/platforms/configs/{org_code}
Response:
{
    "code": 200,
    "message": "success",
    "data": {
        "org_code": "ORG001",
        "org_name": "总公司",
        "source_type": "DINGTALK",
        "source_config": {},
        "product_id": "PROD001",
        "sync_status": "NOT_SYNCED",
        "last_sync_time": null,
        "last_update_batch_no": "BATCH20240325001",
        "status": 1
    }
} 