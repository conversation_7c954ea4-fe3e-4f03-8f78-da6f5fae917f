// 创建部门请求
POST /api/v1/org/departments
Request:
{
    "dept_name": "技术中心",
    "parent_dept_code": "",
    "org_code": "ORG001",
}

Response:
{
    "code": 200,
    "message": "success",
    "data": {
        "dept_code": "DEPT001",
        "dept_name": "技术中心",
        "org_code": "ORG001",
        "parent_dept_code": "",
    }
}

// 更新部门请求
PUT /api/v1/org/departments/{dept_code}
Request:
{
    "dept_name": "技术研发中心",
    "parent_dept_code": ""
}

Response:
{
    "code": 200,
    "message": "success",
    "data": {
        "dept_code": "DEPT001",
        "dept_name": "技术研发中心",
        "org_code": "ORG001",
        "parent_dept_code": "",
    }
}

// 删除部门请求
DELETE /api/v1/org/departments/{dept_code}
Response:
{
    "code": 200,
    "message": "success",
    "data": null
}

// 查询部门列表请求
POST /api/v1/org/departments/search
Request:
{
    "dept_name": "研发",
    "parent_dept_code": "",
    "page":1,
    "limit":20
}

Response:
{
    "code": 200,
    "message": "success",
    "data": {
        "total": 2,
        "items": [
            {
                "dept_code": "DEPT001",
                "dept_name": "技术研发中心",
                "org_code": "ORG001",
                "parent_dept_code": "",
                "has_sub_dept":true
            },
            {
                "dept_code": "DEPT002",
                "dept_name": "研发一部",
                "org_code": "ORG001",
                "parent_dept_code": "DEPT001",
                "has_sub_dept":false
            }
        ]
    }
}

// 查询单个部门详情请求
GET /api/v1/org/departments/{dept_code}
Response:
{
    "code": 200,
    "message": "success",
    "data": {
        "dept_code": "DEPT001",
        "dept_name": "技术研发中心",
        "org_code": "ORG001",
        "parent_dept_code": ""
    }
}
