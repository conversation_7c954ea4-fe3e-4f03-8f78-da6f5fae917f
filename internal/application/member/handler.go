package member

import (
	"sase-client-info/internal/common"
	"sase-client-info/internal/common/errors"
	"sase-client-info/internal/dto"

	"github.com/gin-gonic/gin"
)

// Handler 成员处理器
type Handler struct {
}

// NewHandler 创建新的成员处理器
func NewHandler() *Handler {
	return &Handler{}
}

// CreateMember 创建成员
func (h *Handler) CreateMember(c *gin.Context) {
	response := &dto.MemberResponse{
		UserCode:              "USER002",
		UserName:              "李四",
		Account:               "lisi",
		OrgCode:               "ORG001",
		DeptCode:              "DEPT001",
		DeptPath:              []string{"DEPT000", "DEPT001"},
		SourceType:            "LOCAL",
		SourceCode:            "LOCAL001",
		Mobile:                "***********",
		Email:                 "<EMAIL>",
		Position:              "开发工程师",
		ValidTime:             **********,
		IsActivationEmailSent: true,
		GroupIds:              "GROUP001,GROUP002",
		CreateAt:              **********,
		UpdateAt:              **********,
	}
	common.ResponseResult(c, errors.Success, response)
}

// GetMemberDetail 查询成员详情
func (h *Handler) GetMemberDetail(c *gin.Context) {
	response := &dto.MemberResponse{
		UserCode:              "USER002",
		UserName:              "李四",
		Account:               "lisi",
		OrgCode:               "ORG001",
		DeptCode:              "DEPT001",
		DeptPath:              []string{"DEPT000", "DEPT001"},
		SourceType:            "LOCAL",
		SourceCode:            "LOCAL001",
		Mobile:                "***********",
		Email:                 "<EMAIL>",
		Position:              "开发工程师",
		ValidTime:             **********,
		IsActivationEmailSent: true,
		GroupIds:              "GROUP001,GROUP002",
		CreateAt:              **********,
		UpdateAt:              **********,
	}
	common.ResponseResult(c, errors.Success, response)
}

// UpdateMember 更新成员
func (h *Handler) UpdateMember(c *gin.Context) {
	response := &dto.MemberResponse{
		UserCode:              "USER002",
		UserName:              "李四",
		Account:               "lisi",
		OrgCode:               "ORG001",
		DeptCode:              "DEPT001",
		DeptPath:              []string{"DEPT000", "DEPT001"},
		SourceType:            "LOCAL",
		SourceCode:            "LOCAL001",
		Mobile:                "***********",
		Email:                 "<EMAIL>",
		Position:              "高级开发工程师",
		ValidTime:             **********,
		IsActivationEmailSent: true,
		GroupIds:              "GROUP001,GROUP002",
		CreateAt:              **********,
		UpdateAt:              **********,
	}
	common.ResponseResult(c, errors.Success, response)
}

// DeleteMember 删除成员
func (h *Handler) DeleteMember(c *gin.Context) {
	common.ResponseResult(c, errors.Success, nil)
}

// SearchMembers 查询成员列表
func (h *Handler) SearchMembers(c *gin.Context) {
	response := &dto.MemberListResponse{
		Total: 1,
		Items: []dto.MemberResponse{
			{
				UserCode:              "USER002",
				UserName:              "李四",
				Account:               "lisi",
				OrgCode:               "ORG001",
				DeptCode:              "DEPT001",
				DeptPath:              []string{"DEPT000", "DEPT001"},
				SourceType:            "LOCAL",
				SourceCode:            "LOCAL001",
				Mobile:                "***********",
				Email:                 "<EMAIL>",
				Position:              "高级开发工程师",
				ValidTime:             **********,
				IsActivationEmailSent: true,
				GroupIds:              "GROUP001,GROUP002",
				Status:                1,
				BindDeviceStatus:      1,
				CreateAt:              **********,
				UpdateAt:              **********,
			},
		},
	}
	common.ResponseResult(c, errors.Success, response)
}
