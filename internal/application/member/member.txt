# 用户管理接口示例

// 创建用户请求
POST /api/v1/org/departments/{dept_code}/members
Request:
{
  "user_name": "李四",
  "account": "lisi",
  "org_code": "ORG001",
  "source_type": "LOCAL",
  "source_code": "LOCAL001",
  "mobile": "***********",
  "email": "<EMAIL>",
  "position": "开发工程师",
  "password": "hashed_password_here",
  "valid_time": **********,
  "group_ids": "GROUP001,GROUP002",
  "is_activation_email_sent": true
}

Response:
{
  "code": 200,
  "message": "创建成功",
  "data": {
    "user_code": "USER002",
    "user_name": "李四",
    "account": "lisi",
    "org_code": "ORG001",
    "dept_code": "DEPT001",
    "dept_path": ["DEPT000","DEPT001"],
    "source_type": "LOCAL",
    "source_code": "LOCAL001",
    "mobile": "***********",
    "email": "<EMAIL>",
    "position": "开发工程师",
    "valid_time": **********,
    "is_activation_email_sent": true,
    "group_ids": "GROUP001,GROUP002",
    "create_at": **********,
    "update_at": **********
  }
}

// 查询单个用户请求
GET /api/v1/org/departments/{dept_code}/members/{user_code}
Response:
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "user_code": "USER002",
    "user_name": "李四",
    "account": "lisi",
    "org_code": "ORG001",
    "dept_code": "DEPT001",
    "dept_path": ["DEPT000","DEPT001"],
    "source_type": "LOCAL",
    "source_code": "LOCAL001",
    "mobile": "***********",
    "email": "<EMAIL>",
    "position": "开发工程师",
    "valid_time": **********,
    "is_activation_email_sent": true,
    "group_ids": "GROUP001,GROUP002",
    "create_at": **********,
    "update_at": **********
  }
}

// 更新用户请求
PUT /api/v1/org/departments/{dept_code}/members/{user_code}
Request:
{
  "user_name": "李四",
  "mobile": "***********",
  "email": "<EMAIL>",
  "position": "高级开发工程师",
  "valid_time": **********,
  "is_activation_email_sent": true
}

Response:
{
  "code": 200,
  "message": "更新成功",
  "data": {
    "user_code": "USER002",
    "user_name": "李四",
    "account": "lisi",
    "org_code": "ORG001",
    "dept_code": "DEPT001",
    "dept_path": ["DEPT000","DEPT001"],
    "source_type": "LOCAL",
    "source_code": "LOCAL001",
    "mobile": "***********",
    "email": "<EMAIL>",
    "position": "高级开发工程师",
    "valid_time": **********,
    "is_activation_email_sent": true,
    "group_ids": "GROUP001,GROUP002",
    "create_at": **********,
    "update_at": **********
  }
}

// 删除用户请求
DELETE /api/v1/org/members/{user_code}
Response:
{
  "code": 200,
  "message": "删除成功",
  "data": null
}

// 批量查询用户列表请求
POST /api/v1/org/members/search
Request:
{
  "org_code": "ORG001",
  "user_name": "李",
  "mobile": "***********",
  "email": "<EMAIL>",
  "dept_role":"Role001",
  "status":1,
  "page": 1,
  "limit": 20
}

Response:
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "total": 1,
    "items": [
      {
        "user_code": "USER002",
        "user_name": "李四",
        "account": "lisi",
        "org_code": "ORG001",
        "dept_code": "DEPT001",
        "dept_path": ["DEPT000","DEPT001"],
        "source_type": "LOCAL",
        "source_code": "LOCAL001",
        "mobile": "***********",
        "email": "<EMAIL>",
        "position": "高级开发工程师",
        "valid_time": **********,
        "is_activation_email_sent": true,
        "group_ids": "GROUP001,GROUP002",
        "status":1,
        "bind_device_status":1,
        "create_at": **********,
        "update_at": **********
      }
    ]
  }
}
