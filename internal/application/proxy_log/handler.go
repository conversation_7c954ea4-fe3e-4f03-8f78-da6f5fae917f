package proxy_log

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"net/http"
	"net/url"
	"sase-client-info/internal/common"
	"sase-client-info/internal/common/errors"
	"sase-client-info/internal/dto"
	"sase-client-info/internal/model"
)

type Handler struct {
}

func NewHandler() *Handler {
	return &Handler{}
}

func (h *Handler) GetListProxyLog(ctx *gin.Context) {
	req := &dto.AuditLogListRequest{Page: 1, Limit: 20}
	if err := ctx.ShouldBind(&req); err != nil {
		common.ResponseResult(ctx, errors.InvalidParameter)
		return
	}

	orgName := ctx.GetString("product_id")

	res, err := model.ProxyLogRepo.ListAuditLog(ctx, req, orgName)
	if req.IsExport == 1 {

		h.setCsvData(ctx, res)
		return
	}
	if err != nil {
		common.ResponseResult(ctx, err)
		return
	}
	common.ResponseResult(ctx, res)

}

func (h *<PERSON><PERSON>) setCsvData(ctx *gin.Context, data interface{}) {
	f, fileName, _ := model.ProxyLogRepo.SetValueToCsv2(data, ctx.GetString("language"))
	ctx.Header("Content-Disposition", fmt.Sprintf("attachment; filename*=%s", url.QueryEscape(fileName)))
	ctx.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet") //设置为 excel 格式文件
	// 将Excel文件内容写入HTTP响应
	if f != nil {
		err := f.Write(ctx.Writer)
		if err != nil {
			ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Internal Server Error"})
			return
		}
	}
}
