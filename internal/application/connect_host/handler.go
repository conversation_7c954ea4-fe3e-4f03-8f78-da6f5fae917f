package connect_host

import (
	"context"
	"fmt"
	"sase-client-info/internal/common"
	"sase-client-info/internal/common/consts"
	"sase-client-info/internal/common/errors"
	"sase-client-info/internal/dto"
	"sase-client-info/internal/resource"
	"sase-client-info/pkg/logger"

	"github.com/gin-gonic/gin"
)

// Handler 策略处理器
type Handler struct{}

// NewHandler 创建策略处理器
func NewHandler() *Handler {
	return &Handler{}
}
func (n *Handler) CheckVerifyCode(c *gin.Context) {
	req := &dto.ConnectHostCheckVerifyCode{}
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Errorf("参数错误: %v", err)
		common.ResponseResult(c, errors.ParamsError)
		return
	}

	if req.Source != consts.VerifyCodeSource8 &&
		req.Source != consts.VerifyCodeSource9 &&
		req.Source != consts.VerifyCodeSourceTen {
		common.ResponseResult(c, errors.InvalidParameter)
		return
	}
	if req.VerifyCode == "" {
		common.ResponseResult(c, errors.InvalidParameter)
		return
	}

	orgName := c.GetString("product_id")
	phoneEmail := c.GetString("ciphertext")

	VerifyCodeValidTimeKey := fmt.Sprintf(consts.VerifyCodeValidTime, orgName, phoneEmail, req.Source)
	VerifyCodeIntervalKey := fmt.Sprintf(consts.VerifyCodeInterval, orgName, phoneEmail, req.Source)

	verificationCode, _ := resource.Redisclient.Get(context.Background(), VerifyCodeValidTimeKey).Result()

	if verificationCode != req.VerifyCode {
		common.ResponseResult(c, errors.VerifyCodeValidationFailed)
		return
	}

	defer func() {
		resource.Redisclient.Del(context.Background(), VerifyCodeValidTimeKey)
		resource.Redisclient.Del(context.Background(), VerifyCodeIntervalKey)
	}()

	common.ResponseResult(c, map[string]interface{}{})
}
