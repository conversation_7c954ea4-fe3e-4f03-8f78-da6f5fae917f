package passport

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"sase-client-info/internal/common"
	"sase-client-info/internal/common/consts"
	"sase-client-info/internal/common/errors"
	"sase-client-info/internal/config"
	"sase-client-info/internal/dto"
	"sase-client-info/internal/model"
	"sase-client-info/internal/resource"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"

	"sase-client-info/pkg/auth/jwt"
	"sase-client-info/pkg/logger"
	"sase-client-info/pkg/utils/helper"
)

// Handler 通行证处理器
type Handler struct {
}

// NewHandler 创建新的通行证处理器
func NewHandler() *Handler {
	return &Handler{}
}

func response(c *gin.Context, data []byte) {
	dataStr := string(data)

	dataStr = strings.ReplaceAll(dataStr, `"org_name"`, `"product_id"`)

	c.Data(http.StatusOK, "application/json", []byte(dataStr))
}

// Logout 退出
func (h *Handler) Logout(c *gin.Context) {
	userId := c.GetString("user_id")
	err := resource.Jwt.Destroy(userId, jwt.ACCESS_TOKEN)
	if err != nil {
		logger.Errorf("error from passport logout destroy token: %v", err)
		common.ResponseResult(c, errors.PassportLogoutError)
		return
	}

	// 把passport也退出
	passportRedisKey := fmt.Sprintf(consts.PassportRedisKey, 0, cast.ToInt64(userId))
	resource.Redisclient.Del(context.Background(), passportRedisKey)

	common.ResponseResult(c, errors.Success, map[string]interface{}{
		"logout_jump_url": config.Get().Server.LogoutJumpUrl,
	})
}

// GetJumpUrl 获取通行证跳转URL
func (h *Handler) GetJumpUrl(c *gin.Context) {
	// 解析请求参数
	req := &dto.PassportGetJumpUrlRequest{}
	if err := common.DecodeJsonRequest(c, req); err != nil {
		common.ResponseResult(c, errors.ParamsError)
		return
	}

	currentTime := cast.ToString(time.Now().Unix())
	appid := config.Get().Passport.Appid
	appSecret := config.Get().Passport.AppSecret

	sign := helper.GeneratorPassportSign(appid, appSecret, req.Uuid, currentTime)

	origin := c.GetHeader("Origin")
	if origin == "" {
		origin = c.GetHeader("Referer")
	}
	logger.Info("origin:", origin)
	originInfo, err := url.Parse(origin)
	if err != nil {
		logger.Errorf("error from passport get jump url parse origin: %v,%v", err, origin)
		common.ResponseResult(c, errors.ParamsError)
		return
	}

	originHost := originInfo.Scheme + "://" + originInfo.Host

	consoleWebCallbackUrl := strings.TrimSuffix(originHost+"/"+strings.TrimSuffix(config.Get().Passport.ConsoleWebCallbackUrl, "/"), "/")
	if req.ConsoleJumpUrl != "" {
		consoleWebCallbackUrl += "?callback=" + helper.RawUrlEncode(req.ConsoleJumpUrl)
	}

	params := fmt.Sprintf("sign=%s&appid=%s&time=%s&uuid=%s&callback=%s", sign, appid, currentTime, req.Uuid, helper.RawUrlEncode(consoleWebCallbackUrl))

	passportJumpUrl := ""
	if req.JumpType == "user_info" {
		passportJumpUrl = strings.TrimSuffix(config.Get().Passport.PassportWebUrl, "/") + "/user-info"
	} else {
		passportJumpUrl = strings.TrimSuffix(config.Get().Passport.PassportWebUrl, "/")
	}

	url := fmt.Sprintf("%s?%s", passportJumpUrl, params)

	common.ResponseResult(c, errors.Success, url)
}

func (h *Handler) VerifyToken(c *gin.Context) {
	req := &dto.PassportVerifyTokenRequest{}
	if err := common.DecodeJsonRequest(c, req); err != nil {
		common.ResponseResult(c, errors.ParamsError)
		return
	}

	//通过token获取user
	userInfo, err := h.getUser(c, req)
	if err != nil {
		common.ResponseResult(c, err)
		return
	}

	if cast.ToString(userInfo["app_id"]) == "" {
		common.ResponseResult(c, errors.PassportGetUserError)
		return
	}

	//登录成功
	claimsElement := &jwt.ClaimsElement{
		UserId:       cast.ToString(userInfo["id"]),
		UserName:     cast.ToString(userInfo["nickname"]),
		OrgId:        cast.ToInt64(userInfo["org_id"]),
		ProductId:    cast.ToString(userInfo["app_id"]),
		TokenType:    jwt.ACCESS_TOKEN,
		GroupId:      cast.ToString(userInfo["role_id"]),
		OrgName:      cast.ToString(userInfo["org_name"]),
		UserParentId: cast.ToString(userInfo["id"]),
		Channel:      consts.ChannelRmSase,
	}

	info := map[string]interface{}{
		"dcs_api":            cast.ToString(userInfo["dcs_api"]),
		"dcs_id":             cast.ToInt(userInfo["dcs_id"]),
		"ai_api":             cast.ToString(userInfo["ai_api"]),
		"websocket_addr":     cast.ToString(userInfo["websocket_addr"]),
		"ciphertext":         cast.ToString(userInfo["ciphertext"]),
		"passport_user_info": userInfo,
	}

	accessJwtToken, err := resource.Jwt.Generate(claimsElement, info)
	if err != nil {
		logger.Errorf("error from parent user login create token error: %v", err)
		common.ResponseResult(c, errors.UserLoginCreateTokenError)
		return
	}

	authLoginResp := &dto.AuthLoginResp{
		AccessToken: accessJwtToken.AccessToken,
		ProductId:   cast.ToString(userInfo["app_id"]),
		UserName:    cast.ToString(userInfo["nickname"]),
		UserId:      cast.ToString(userInfo["id"]),
		IsAdmin:     cast.ToInt(userInfo["is_admin"]),
	}

	common.ResponseResult(c, errors.Success, authLoginResp)
}

func (h *Handler) GetPassportUrl(c *gin.Context) {
	res := map[string]any{
		"invite": strings.TrimSuffix(config.Get().Passport.PassportWebUrl, "/") + config.Get().PassportUriMap["invite"],
	}

	common.ResponseResult(c, errors.Success, res)
}

func (h *Handler) GetUserInfo(c *gin.Context) {
	userInfo := c.GetStringMap("passport_user_info")
	if userInfo == nil {
		common.ResponseResult(c, errors.PassportGetUserError)
		return
	}

	res := map[string]interface{}{
		"user_id":    c.GetString("user_id"),
		"user_name":  c.GetString("user_name"),
		"product_id": c.GetString("product_id"),
		"is_admin":   cast.ToInt(userInfo["is_admin"]),
	}

	common.ResponseResult(c, errors.Success, res)
}

func (h *Handler) getUser(c *gin.Context, req *dto.PassportVerifyTokenRequest) (map[string]interface{}, error) {
	requestUrl := strings.TrimSuffix(config.Get().Passport.PassportBackendUrl, "/") + "/get_user"

	currentTime := cast.ToString(time.Now().Unix())
	appid := config.Get().Passport.Appid
	appSecret := config.Get().Passport.AppSecret

	sign := helper.GeneratorPassportSign(appid, appSecret, req.Uuid, currentTime)

	origin := c.GetHeader("Origin")
	if origin == "" {
		origin = c.GetHeader("Referer")
	}
	logger.Info("origin:", origin)
	originInfo, err := url.Parse(origin)
	if err != nil {
		logger.Errorf("error from passport get jump url parse origin: %v,%v", err, origin)
		return nil, errors.PassportGetUserError
	}

	originHost := originInfo.Scheme + "://" + originInfo.Host

	requestData := map[string]interface{}{
		"token":    req.Token,
		"sign":     sign,
		"appid":    cast.ToInt(appid),
		"time":     cast.ToInt(currentTime),
		"uuid":     req.Uuid,
		"callback": originHost,
	}

	jsonStr, _ := json.Marshal(requestData)

	respByte, err := helper.CurlRequest(requestUrl, "POST", jsonStr, map[string]string{}, config.Get().Settings.InsecureSkipVerify)
	if err != nil {
		logger.Errorf("error from service verify token request get_user: %v %v %v", err, requestUrl, string(jsonStr))
		return nil, errors.PassportGetUserError
	}

	logger.Info("get passport user request data:", string(jsonStr), " response data:", string(respByte))

	var res = &dto.PassportResponse{}

	err = json.Unmarshal(respByte, &res)

	if res == nil || err != nil {
		logger.Errorf("error from service verify token unmarshal json: %v %v", err, string(respByte))
		return nil, errors.PassportGetUserError
	}
	data := cast.ToStringMap(res.Data)

	if cast.ToInt(data["id"]) <= 0 {
		logger.Errorf("error from service verify token user info error: %v %v", res, string(respByte))
		return nil, errors.PassportGetUserError
	}
	return data, nil
}

func (h *Handler) GetUserAllOrgAppList(c *gin.Context) {
	userInfo := c.GetStringMap("passport_user_info")
	if userInfo == nil {
		common.ResponseResult(c, errors.PassportGetUserError)
		return
	}

	res, err := model.PassportRepo.GetUserAllOrgAppList(userInfo)
	if err != nil {
		common.ResponseResult(c, err)
		return
	}

	response(c, res)
}

func (h *Handler) GetLicenceInfo(c *gin.Context) {
	productId := c.GetString("product_id")
	if productId == "" {
		common.ResponseResult(c, errors.PassportGetUserError)
		return
	}

	userInfo := c.GetStringMap("passport_user_info")
	if userInfo == nil {
		common.ResponseResult(c, errors.PassportGetUserError)
		return
	}

	req := &dto.ExternalGetOrgAppInfoByOrgName{
		ProductId: productId,
	}

	res, err := model.PassportRepo.GetOrgAppInfoByOrgName(userInfo, req)
	if err != nil {
		common.ResponseResult(c, err)
		return
	}

	fmt.Println(">>>>>>>>>>>>>>>>", res.PermissionData)
	permissionData := []string{}
	json.Unmarshal([]byte(res.PermissionData), &permissionData)

	result := map[string]interface{}{
		"product_id":      productId,
		"buy_type":        res.BuyType,
		"start_time":      res.StartTime,
		"expire_time":     res.ExpireTime,
		"client_count":    res.ClientCount,
		"permission_data": permissionData,
	}
	common.ResponseResult(c, errors.Success, result)
}

func (h *Handler) SwitchOrgName(c *gin.Context) {
	userInfo := c.GetStringMap("passport_user_info")
	if userInfo == nil {
		common.ResponseResult(c, errors.PassportGetUserError)
		return
	}

	req := &dto.ExternalSwitchOrgName{}
	if err := common.DecodeJsonRequest(c, req); err != nil {
		common.ResponseResult(c, errors.ParamsError)
		return
	}
	res, err := model.PassportRepo.SwitchOrgName(userInfo, req)
	if err != nil {
		common.ResponseResult(c, err)
		return
	}

	response(c, res)
}

func (h *Handler) ReceiveInvitationList(c *gin.Context) {
	userInfo := c.GetStringMap("passport_user_info")
	if userInfo == nil {
		common.ResponseResult(c, errors.PassportGetUserError)
		return
	}

	req := &dto.ExternalReceiveInvitationList{}
	if err := common.DecodeJsonRequest(c, req); err != nil {
		common.ResponseResult(c, errors.ParamsError)
		return
	}

	res, err := model.PassportRepo.ReceiveInvitationList(userInfo, req)
	if err != nil {
		common.ResponseResult(c, err)
		return
	}

	response(c, res)

}

func (h *Handler) UpdateReceiveInvitationStatus(c *gin.Context) {
	userInfo := c.GetStringMap("passport_user_info")
	if userInfo == nil {
		common.ResponseResult(c, errors.PassportGetUserError)
		return
	}

	req := &dto.ExternalUpdateReceiveInvitationStatus{}
	if err := common.DecodeJsonRequest(c, req); err != nil {
		common.ResponseResult(c, errors.ParamsError)
		return
	}
	res, err := model.PassportRepo.UpdateReceiveInvitationStatus(userInfo, req)
	if err != nil {
		common.ResponseResult(c, err)
		return
	}

	response(c, res)
}

func (h *Handler) GetOrgMemberData(c *gin.Context) {
	userInfo := c.GetStringMap("passport_user_info")
	if userInfo == nil {
		common.ResponseResult(c, errors.PassportGetUserError)
		return
	}

	req := &dto.ExternalGetOrgMemberData{}
	if err := common.DecodeJsonRequest(c, req); err != nil {
		common.ResponseResult(c, errors.ParamsError)
		return
	}
	res, err := model.PassportRepo.GetOrgMemberData(userInfo, req)
	if err != nil {
		common.ResponseResult(c, err)
		return
	}

	response(c, res)
}

func (h *Handler) InsertMember(c *gin.Context) {
	userInfo := c.GetStringMap("passport_user_info")
	if userInfo == nil {
		common.ResponseResult(c, errors.PassportGetUserError)
		return
	}

	req := &dto.ExternalInsertMember{}
	if err := common.DecodeJsonRequest(c, req); err != nil {
		common.ResponseResult(c, errors.ParamsError)
		return
	}
	res, err := model.PassportRepo.InsertMember(userInfo, req)
	if err != nil {
		common.ResponseResult(c, err)
		return
	}

	response(c, res)
}

func (h *Handler) RemoveOrgUser(c *gin.Context) {
	userInfo := c.GetStringMap("passport_user_info")
	if userInfo == nil {
		common.ResponseResult(c, errors.PassportGetUserError)
		return
	}

	req := &dto.ExternalRemoveOrgUser{}
	if err := common.DecodeJsonRequest(c, req); err != nil {
		common.ResponseResult(c, errors.ParamsError)
		return
	}
	res, err := model.PassportRepo.RemoveOrgUser(userInfo, req)
	if err != nil {
		common.ResponseResult(c, err)
		return
	}

	response(c, res)
}

func (h *Handler) GetProductRoles(c *gin.Context) {
	userInfo := c.GetStringMap("passport_user_info")
	if userInfo == nil {
		common.ResponseResult(c, errors.PassportGetUserError)
		return
	}

	req := &dto.ExternalGetProductRoles{}
	if err := common.DecodeJsonRequest(c, req); err != nil {
		common.ResponseResult(c, errors.ParamsError)
		return
	}
	res, err := model.PassportRepo.GetProductRoles(userInfo, req)
	if err != nil {
		common.ResponseResult(c, err)
		return
	}

	response(c, res)
}

func (h *Handler) GetUserOrgApp(c *gin.Context) {
	userInfo := c.GetStringMap("passport_user_info")
	if userInfo == nil {
		common.ResponseResult(c, errors.PassportGetUserError)
		return
	}

	req := &dto.ExternalGetUserOrgApp{}
	if err := common.DecodeJsonRequest(c, req); err != nil {
		common.ResponseResult(c, errors.ParamsError)
		return
	}
	res, err := model.PassportRepo.GetUserOrgApp(userInfo, req)
	if err != nil {
		common.ResponseResult(c, err)
		return
	}

	response(c, res)
}

func (h *Handler) UpdateUserOrgAppRole(c *gin.Context) {
	userInfo := c.GetStringMap("passport_user_info")
	if userInfo == nil {
		common.ResponseResult(c, errors.PassportGetUserError)
		return
	}

	req := &dto.ExternalUpdateUserOrgAppRole{}
	if err := common.DecodeJsonRequest(c, req); err != nil {
		common.ResponseResult(c, errors.ParamsError)
		return
	}
	res, err := model.PassportRepo.UpdateUserOrgAppRole(userInfo, req)
	if err != nil {
		common.ResponseResult(c, err)
		return
	}

	response(c, res)
}

func (h *Handler) InvitationList(c *gin.Context) {
	userInfo := c.GetStringMap("passport_user_info")
	if userInfo == nil {
		common.ResponseResult(c, errors.PassportGetUserError)
		return
	}

	req := &dto.ExternalInvitationList{}
	if err := common.DecodeJsonRequest(c, req); err != nil {
		common.ResponseResult(c, errors.ParamsError)
		return
	}
	res, err := model.PassportRepo.InvitationList(userInfo, req)
	if err != nil {
		common.ResponseResult(c, err)
		return
	}

	response(c, res)
}

func (h *Handler) UpdateInvitationStatus(c *gin.Context) {
	userInfo := c.GetStringMap("passport_user_info")
	if userInfo == nil {
		common.ResponseResult(c, errors.PassportGetUserError)
		return
	}

	req := &dto.ExternalUpdateInvitationStatus{}
	if err := common.DecodeJsonRequest(c, req); err != nil {
		common.ResponseResult(c, errors.ParamsError)
		return
	}
	res, err := model.PassportRepo.UpdateInvitationStatus(userInfo, req)
	if err != nil {
		common.ResponseResult(c, err)
		return
	}

	response(c, res)
}

func (h *Handler) GetMessageList(c *gin.Context) {
	userInfo := c.GetStringMap("passport_user_info")
	if userInfo == nil {
		common.ResponseResult(c, errors.PassportGetUserError)
		return
	}

	req := &dto.ExternalGetMessageList{}
	if err := common.DecodeJsonRequest(c, req); err != nil {
		common.ResponseResult(c, errors.ParamsError)
		return
	}
	res, err := model.PassportRepo.GetMessageList(userInfo, req)
	if err != nil {
		common.ResponseResult(c, err)
		return
	}

	response(c, res)
}

func (h *Handler) GetUnreadNum(c *gin.Context) {
	userInfo := c.GetStringMap("passport_user_info")
	if userInfo == nil {
		common.ResponseResult(c, errors.PassportGetUserError)
		return
	}

	req := &dto.ExternalGetUnreadNum{}
	if err := common.DecodeJsonRequest(c, req); err != nil {
		common.ResponseResult(c, errors.ParamsError)
		return
	}
	res, err := model.PassportRepo.GetUnreadNum(userInfo, req)
	if err != nil {
		common.ResponseResult(c, err)
		return
	}

	response(c, res)
}

func (h *Handler) MarkRead(c *gin.Context) {
	userInfo := c.GetStringMap("passport_user_info")
	if userInfo == nil {
		common.ResponseResult(c, errors.PassportGetUserError)
		return
	}

	req := &dto.ExternalMarkRead{}
	if err := common.DecodeJsonRequest(c, req); err != nil {
		common.ResponseResult(c, errors.ParamsError)
		return
	}
	res, err := model.PassportRepo.MarkRead(userInfo, req)
	if err != nil {
		common.ResponseResult(c, err)
		return
	}

	response(c, res)
}

func (h *Handler) MessageDetail(c *gin.Context) {
	userInfo := c.GetStringMap("passport_user_info")
	if userInfo == nil {
		common.ResponseResult(c, errors.PassportGetUserError)
		return
	}

	req := &dto.ExternalMessageDetail{}
	if err := common.DecodeJsonRequest(c, req); err != nil {
		common.ResponseResult(c, errors.ParamsError)
		return
	}
	res, err := model.PassportRepo.MessageDetail(userInfo, req)
	if err != nil {
		common.ResponseResult(c, err)
		return
	}

	response(c, res)

}
