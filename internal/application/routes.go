package application

import (
	"net/http"

	"sase-client-info/internal/application/proxy_log"

	"github.com/gin-gonic/gin"

	"sase-client-info/internal/application/connect_host"
	"sase-client-info/internal/application/passport"
	"sase-client-info/internal/application/verify_code"
	"sase-client-info/internal/application/websocket"
	"sase-client-info/internal/common"
	"sase-client-info/internal/common/consts"
	"sase-client-info/internal/config"
	"sase-client-info/internal/middleware"
	"sase-client-info/pkg/logger"
)

// RegisterRoutes 注册所有路由
func RegisterRoutes(r *gin.Engine) {
	logger.Info("开始注册API路由")

	// 全局中间件
	r.Use(middleware.Cors())
	r.Use(middleware.RequestID())
	r.Use(middleware.Logger())
	r.Use(middleware.Recovery())
	r.Use()

	// 代理edr相关接口
	edrRoutes := r.Group(config.Get().Server.SaseContext+consts.EdrConsolePrefix, middleware.JWT())
	{
		edrRoutes.Any("/*path", common.ProxyEdrRequest)
	}

	// API版本v1
	v1 := r.Group(config.Get().Server.SaseContext + "/api/v1")
	{

		v1.GET("/test", middleware.JWT(), func(c *gin.Context) {
			logger.Debug("test请求")
			c.JSON(http.StatusOK, gin.H{
				"status": "ok",
			})
		})

		// 健康检查
		v1.GET("/health", func(c *gin.Context) {
			logger.Debug("健康检查请求")
			c.JSON(http.StatusOK, gin.H{
				"status": "ok",
			})
		})
		logger.Debug("已注册健康检查路由: /api/v1/health")

		// websocket 相关路由
		websocketRoutes := v1.Group("/websocket", middleware.JWT())
		{
			logger.Debug("注册websocket相关路由")
			websocketHandler := websocket.NewHandler()
			websocketRoutes.GET("/get_addr", websocketHandler.GetAddr)
			logger.Debug("已注册websocket相关路由: GET /get_addr")
		}

		// passport 相关路由
		passportRoutes := v1.Group("/passport")
		{
			logger.Debug("注册通行证相关路由")
			passportHandler := passport.NewHandler()
			passportRoutes.POST("/get_jump_url", passportHandler.GetJumpUrl)
			passportRoutes.POST("/verify_token", passportHandler.VerifyToken)
			passportRoutes.GET("/get_user_info", middleware.JWT(), passportHandler.GetUserInfo)
			passportRoutes.GET("/get_passport_url", passportHandler.GetPassportUrl)
			passportRoutes.POST("/logout", middleware.JWT(), passportHandler.Logout)
			// 用户可切换的产品列表
			passportRoutes.GET("/get_user_all_org_app_list", middleware.JWT(), passportHandler.GetUserAllOrgAppList)
			// 用户获取许可证信息
			passportRoutes.GET("/get_licence_info", middleware.JWT(), passportHandler.GetLicenceInfo)
			// 用户切换组织名称
			passportRoutes.POST("/switch_org_name", middleware.JWT(), passportHandler.SwitchOrgName)
			// 用户接受邀请列表
			passportRoutes.POST("/receive_invitation_list", middleware.JWT(), passportHandler.ReceiveInvitationList)
			passportRoutes.POST("/update_receive_invitation_status", middleware.JWT(), passportHandler.UpdateReceiveInvitationStatus)
			// 用户组织成员数据（只有主账号能看）
			passportRoutes.POST("/get_org_members", middleware.JWT(), passportHandler.GetOrgMemberData)
			passportRoutes.POST("/insert_member", middleware.JWT(), passportHandler.InsertMember)
			passportRoutes.POST("/remove_org_user", middleware.JWT(), passportHandler.RemoveOrgUser)
			passportRoutes.POST("/get_product_roles", middleware.JWT(), passportHandler.GetProductRoles)
			passportRoutes.POST("/get_user_org_app", middleware.JWT(), passportHandler.GetUserOrgApp)
			passportRoutes.POST("/update_user_org_app_role", middleware.JWT(), passportHandler.UpdateUserOrgAppRole)
			passportRoutes.POST("/invitation_list", middleware.JWT(), passportHandler.InvitationList)
			passportRoutes.POST("/update_invitation_status", middleware.JWT(), passportHandler.UpdateInvitationStatus)
			// 用户消息
			passportRoutes.POST("/get_message_list", middleware.JWT(), passportHandler.GetMessageList)
			passportRoutes.POST("/get_unread_num", middleware.JWT(), passportHandler.GetUnreadNum)
			passportRoutes.POST("/mark_read", middleware.JWT(), passportHandler.MarkRead)
			passportRoutes.POST("/message_detail", middleware.JWT(), passportHandler.MessageDetail)

			logger.Debug("已注册通行证相关路由: POST /get_jump_url, POST /verify_token, POST /logout")
		}

		// 验证码相关路由
		verifyCodeRoutes := v1.Group("/verify_code", middleware.JWT())
		{
			logger.Debug("注册验证码相关路由")
			verifyCodeHandler := verify_code.NewHandler()
			verifyCodeRoutes.POST("/is_need_verify", verifyCodeHandler.IsNeedVerify)
			verifyCodeRoutes.POST("/send", verifyCodeHandler.Send)
			logger.Debug("已注册验证码相关路由: POST /is_need_verify, POST /send")
		}

		// 连接主机相关路由
		connectHostRoutes := v1.Group("/connect_host", middleware.JWT())
		{
			logger.Debug("注册连接主机相关路由")
			connectHostHandler := connect_host.NewHandler()
			connectHostRoutes.POST("/check_verify_code", connectHostHandler.CheckVerifyCode)
		}

		// 钉钉回调相关路由
		callbackRoutes := v1.Group("/callback", middleware.JWT())
		{
			logger.Debug("注册钉钉回调相关路由")
			callbackRoutes.GET("/dingtalk", common.ProxyCloudRequest)
			logger.Debug("已注册钉钉回调相关路由: GET /dingtalk")
		}

		// 应用分类相关路由
		appCategoryRoutes := v1.Group("/application/apps/classes", middleware.JWT())
		{
			logger.Debug("注册应用分类相关路由")
			appCategoryRoutes.GET("", common.ProxyCloudRequest)
			appCategoryRoutes.POST("", common.ProxyCloudRequest)
			appCategoryRoutes.PUT("/:category_code", common.ProxyCloudRequest)
			appCategoryRoutes.DELETE("/:category_code", common.ProxyCloudRequest)
			logger.Debug("已注册应用分类相关路由: GET /, POST /, PUT /:category_code, DELETE /:category_code")
		}

		// 应用标签相关路由
		appCategoryTagRoutes := v1.Group("/application/apps/tags", middleware.JWT())
		{
			logger.Debug("注册应用标签相关路由")
			appCategoryTagRoutes.GET("", common.ProxyCloudRequest)
			appCategoryTagRoutes.POST("", common.ProxyCloudRequest)
			appCategoryTagRoutes.PUT("/:tag_code", common.ProxyCloudRequest)
			appCategoryTagRoutes.DELETE("/:tag_code", common.ProxyCloudRequest)
			logger.Debug("已注册应用标签相关路由: GET /, POST /, PUT /:tag_code, DELETE /:tag_code")
		}

		// 应用基础相关路由
		appBaseRoutes := v1.Group("/application/apps", middleware.JWT())
		{
			logger.Debug("注册应用基础相关路由")
			appBaseRoutes.GET("", common.ProxyCloudRequest)
			appBaseRoutes.GET("/:app_code", common.ProxyCloudRequest)
			appBaseRoutes.POST("", common.ProxyCloudRequest)
			appBaseRoutes.PUT("/:app_code", common.ProxyCloudRequest)
			appBaseRoutes.PUT("/:app_code/tags", common.ProxyCloudRequest)
			appBaseRoutes.PUT("/:app_code/address", common.ProxyCloudRequest)
			appBaseRoutes.PUT("/:app_code/health", common.ProxyCloudRequest)
			appBaseRoutes.DELETE("", common.ProxyCloudRequest)
			appBaseRoutes.POST("/import", common.ProxyCloudRequest)
			appBaseRoutes.GET("/export", common.ProxyCloudRequest)
			appBaseRoutes.GET("/get_relation_policys_by_app_codes", common.ProxyCloudRequest)
			logger.Debug("已注册应用基础相关路由: GET /, GET /:app_code, POST /, PUT /:app_code, DELETE /:app_code")
		}

		// 应用管理员相关路由
		appAdminRoutes := v1.Group("/application/admin", middleware.JWT())
		{
			logger.Debug("注册应用管理员相关路由")
			appAdminRoutes.GET("", common.ProxyCloudRequest)
			appAdminRoutes.POST("", common.ProxyCloudRequest)
			appAdminRoutes.PUT("/:app_admin_code", common.ProxyCloudRequest)
			appAdminRoutes.DELETE("/:app_admin_code", common.ProxyCloudRequest)
			logger.Debug("已注册应用管理员相关路由: GET /, POST /, PUT /:app_admin_code, DELETE /:app_admin_code")
		}

		appHealthRoutes := v1.Group("/application/health", middleware.JWT())
		{
			logger.Debug("注册应用健康状态相关路由")
			appHealthRoutes.GET("/batch_apps", common.ProxyCloudRequest) // 批量查询应用健康状态
			logger.Debug("已注册应用健康状态相关路由: GET /batch_apps")
		}

		// 应用统计相关路由
		appAnalysisRoutes := v1.Group("/application/apps/analysis", middleware.JWT())
		{
			logger.Debug("注册应用统计相关路由")
			// 应用类型分析统计
			appAnalysisRoutes.GET("/types", common.ProxyCloudRequest)
			// 应用状态分析统计
			appAnalysisRoutes.GET("/status", common.ProxyCloudRequest)
			// 应用标签分析统计
			appAnalysisRoutes.GET("/tags", common.ProxyCloudRequest)
			logger.Debug("已注册应用统计相关路由: GET /types, GET /status, GET /tags")
		}

		// 门户应用分类相关路由
		appPortalCategoryRoutes := v1.Group("/application/portal/category", middleware.JWT())
		{
			logger.Debug("注册门户应用分类相关路由")
			appPortalCategoryRoutes.GET("", common.ProxyCloudRequest)
			appPortalCategoryRoutes.POST("", common.ProxyCloudRequest)
			appPortalCategoryRoutes.GET("/:category_code", common.ProxyCloudRequest)
			appPortalCategoryRoutes.PUT("/:category_code", common.ProxyCloudRequest)
			appPortalCategoryRoutes.DELETE("/:category_code", common.ProxyCloudRequest)
			appPortalCategoryRoutes.GET("/stats", common.ProxyCloudRequest)
			logger.Debug("已注册门户应用分类相关路由: GET /, POST /, GET /:category_code, PUT /:category_code, DELETE /:category_code")
		}

		// 门户配置相关路由
		appPortalConfigGroup := v1.Group("/application/portal/config", middleware.JWT())
		{
			logger.Debug("注册门户配置相关路由")
			appPortalConfigGroup.POST("", common.ProxyCloudRequest) // 创建或更新门户配置
			appPortalConfigGroup.PUT("", common.ProxyCloudRequest)  // 更新门户配置
			appPortalConfigGroup.GET("", common.ProxyCloudRequest)  // 获取门户配置
			logger.Debug("已注册门户配置相关路由: POST /, PUT /, GET /")
		}

		// 部门相关路由
		departmentRoutes := v1.Group("/org/departments", middleware.JWT())
		{
			logger.Debug("注册部门相关路由")
			departmentRoutes.POST("", common.ProxyCloudRequest)                 // 创建部门
			departmentRoutes.PUT("/:dept_code", common.ProxyCloudRequest)       // 更新部门
			departmentRoutes.DELETE("/:dept_code", common.ProxyCloudRequest)    // 删除部门
			departmentRoutes.POST("/search", common.ProxyCloudRequest)          // 查询部门列表
			departmentRoutes.POST("/search_by_codes", common.ProxyCloudRequest) // 根据部门编码搜索部门
			departmentRoutes.GET("/:dept_code", common.ProxyCloudRequest)       // 查询部门详情
			departmentRoutes.POST("/get_departments", common.ProxyCloudRequest) // 批量查询部门
			logger.Debug("已注册部门相关路由: POST /, PUT /:dept_code, DELETE /:dept_code, POST /search, GET /:dept_code, POST /custom_import, GET /export")
		}

		// 部门角色相关路由
		departmentRoleRoutes := v1.Group("/org/departments/roles", middleware.JWT())
		{
			logger.Debug("注册部门角色相关路由")
			departmentRoleRoutes.POST("", common.ProxyCloudRequest)
			departmentRoleRoutes.PUT("/:role_code", common.ProxyCloudRequest)
			departmentRoleRoutes.DELETE("/:role_code", common.ProxyCloudRequest)
			departmentRoleRoutes.POST("/search", common.ProxyCloudRequest)
			departmentRoleRoutes.GET("/:role_code", common.ProxyCloudRequest)
			logger.Debug("已注册部门角色相关路由: POST /, PUT /:role_code, DELETE /:role_code, POST /search, GET /:role_code")
		}

		// 部门角色关系相关路由
		departmentRoleRelationRoutes := v1.Group("/org/departments/:dept_code/roles/:role_code", middleware.JWT())
		{
			logger.Debug("注册部门角色关系相关路由")
			departmentRoleRelationRoutes.POST("/members", common.ProxyCloudRequest)
			departmentRoleRelationRoutes.POST("/can_select", common.ProxyCloudRequest)
			departmentRoleRelationRoutes.POST("/selected", common.ProxyCloudRequest)
			logger.Debug("已注册部门角色关系相关路由: POST /, POST /can_select, POST /selected")
		}

		// 部门成员相关路由
		departmentMemberRoutes := v1.Group("/org/departments/:dept_code/members", middleware.JWT())
		{
			logger.Debug("注册部门成员相关路由")
			departmentMemberRoutes.POST("", common.ProxyCloudRequest)
			departmentMemberRoutes.GET("/:user_code", common.ProxyCloudRequest)
			departmentMemberRoutes.PUT("/:user_code", common.ProxyCloudRequest)
			departmentMemberRoutes.DELETE("/:user_code", common.ProxyCloudRequest)
			departmentMemberRoutes.POST("/search", common.ProxyCloudRequest)
			departmentMemberRoutes.POST("/change_dept", common.ProxyCloudRequest)
			departmentMemberRoutes.POST("/invite", common.ProxyCloudRequest)
			departmentMemberRoutes.POST("/pause", common.ProxyCloudRequest)
			departmentMemberRoutes.POST("/reset_password", common.ProxyCloudRequest)
			departmentMemberRoutes.POST("/delete", common.ProxyCloudRequest)

			logger.Debug("已注册部门成员相关路由: POST /, GET /:user_code, PUT /:user_code, DELETE /:user_code, POST /search, POST /change_dept, POST /invite, POST /pause")
		}
		// 组织平台配置相关路由
		orgPlatformConfigRoutes := v1.Group("/org", middleware.JWT())
		{
			logger.Debug("注册组织平台配置相关路由")
			orgPlatformConfigRoutes.POST("", common.ProxyCloudRequest)                             // 创建组织平台配置
			orgPlatformConfigRoutes.PUT("/:org_code", common.ProxyCloudRequest)                    // 更新组织平台配置
			orgPlatformConfigRoutes.DELETE("/:org_code", common.ProxyCloudRequest)                 // 删除组织平台配置
			orgPlatformConfigRoutes.POST("/search", common.ProxyCloudRequest)                      // 搜索组织平台配置
			orgPlatformConfigRoutes.GET("/:org_code", common.ProxyCloudRequest)                    // 获取组织平台配置详情
			orgPlatformConfigRoutes.POST("/:org_code/sync", common.ProxyCloudRequest)              // 组织平台架构同步
			orgPlatformConfigRoutes.GET("/count", common.ProxyCloudRequest)                        // 统计组织的个数
			orgPlatformConfigRoutes.POST("/:org_code/custom_org_import", common.ProxyCloudRequest) // 组织部门成员-导入

			logger.Debug("已注册组织平台配置相关路由: POST /, PUT /:org_code, DELETE /:org_code, POST /search, GET /:org_code")

		}

		certRouter := v1.Group("/application/certs", middleware.JWT())
		{
			logger.Debug("证书相关路由")

			certRouter.POST("", common.ProxyCloudRequest)
			certRouter.GET("/:cert_id", common.ProxyCloudRequest)
			certRouter.PUT("/:cert_id", common.ProxyCloudRequest)
			certRouter.DELETE("/:cert_id", common.ProxyCloudRequest)
			certRouter.POST("/search", common.ProxyCloudRequest)

		}

		ipConfigGroup := v1.Group("/application/ip_configs", middleware.JWT())
		{
			ipConfigGroup.POST("", common.ProxyCloudRequest)
			ipConfigGroup.PUT("/:sign_id", common.ProxyCloudRequest)
			ipConfigGroup.DELETE("/:sign_id", common.ProxyCloudRequest)
			ipConfigGroup.GET("/:sign_id", common.ProxyCloudRequest)
			ipConfigGroup.POST("/search", common.ProxyCloudRequest)
		}

		orgPlatformMemberRoutes := v1.Group("/org/:org_code", middleware.JWT())
		{
			logger.Debug("注册组织平台成员相关路由")
			orgPlatformMemberRoutes.GET("/export/:dept_code", common.ProxyCloudRequest)               // 部门组织导出
			orgPlatformMemberRoutes.GET("/members/count", common.ProxyCloudRequest)                   // 统计平台组织的成员数量
			orgPlatformMemberRoutes.POST("/sync_record", common.ProxyCloudRequest)                    // 查看同步记录
			orgPlatformMemberRoutes.GET("/sync_record/:record_code/detail", common.ProxyCloudRequest) // 查看同步记录详细信息

		}

		// 策略相关路由
		policyRoutes := v1.Group("/policy/", middleware.JWT())
		{
			logger.Debug("注册策略相关路由")
			// 基础策略操作
			policyRoutes.POST("/search", common.ProxyCloudRequest)          // 查询策略列表
			policyRoutes.POST("", common.ProxyCloudRequest)                 // 创建策略
			policyRoutes.PUT("/:policy_code", common.ProxyCloudRequest)     // 更新策略
			policyRoutes.DELETE("/:policy_code", common.ProxyCloudRequest)  // 删除策略
			policyRoutes.GET("/:policy_code", common.ProxyCloudRequest)     // 查询策略详情
			policyRoutes.DELETE("/batch", common.ProxyCloudRequest)         // 批量删除策略
			policyRoutes.GET("/app/:app_code", common.ProxyCloudRequest)    // 应用策略
			policyRoutes.POST("/app/user_detail", common.ProxyCloudRequest) // 应用策略用户

			// 策略状态管理
			policyRoutes.PUT("/:policy_code/status", common.ProxyCloudRequest) // 更新策略状态
			// 策略分析
			policyRoutes.GET("/analysis/detail", common.ProxyCloudRequest) // 策略统计

			// 用户视图相关
			policyRoutes.GET("/user/:user_code/view", common.ProxyCloudRequest)        // 用户视图
			policyRoutes.GET("/user/:user_code/view/export", common.ProxyCloudRequest) // 用户视图导出
			policyRoutes.GET("/:policy_code/user_info", common.ProxyCloudRequest)      //

			// 导出功能
			policyRoutes.GET("/export", common.ProxyCloudRequest)               // 策略导出
			policyRoutes.GET("/list", common.ProxyCloudRequest)                 // 策略
			policyRoutes.GET("/app_policy/:app_code", common.ProxyCloudRequest) // 应用策略

			policyRoutes.GET("/factor_static_detail", common.ProxyCloudRequest) // 策略因子详情
			policyRoutes.GET("/factor_static_list", common.ProxyCloudRequest)   // 策略规则列表

			logger.Debug("已注册策略相关路由: POST /search, POST /, PUT /:policy_code, DELETE /:policy_code, GET /:policy_code, GET /analysis/detail, POST /user_view/:user_code, DELETE /batch, POST /:policy_code/status")

			// 场景模板相关路由
			sceneTemplateRoutes := policyRoutes.Group("/scene/template")
			{
				logger.Debug("注册场景模板相关路由")
				sceneTemplateRoutes.POST("", common.ProxyCloudRequest)               // 创建场景模板
				sceneTemplateRoutes.PUT("/:scene_code", common.ProxyCloudRequest)    // 更新场景模板
				sceneTemplateRoutes.DELETE("/:scene_code", common.ProxyCloudRequest) // 删除场景模板
				sceneTemplateRoutes.GET("/:scene_code", common.ProxyCloudRequest)    // 获取场景模板详情
				sceneTemplateRoutes.POST("/search", common.ProxyCloudRequest)        // 搜索场景模板
				sceneTemplateRoutes.DELETE("/batch", common.ProxyCloudRequest)       // 批量删除策略

				logger.Debug("已注册场景模板相关路由: POST /, PUT /:scene_code, DELETE /:scene_code, GET /:scene_code, POST /search")
			}

			policyNetworkRegion := policyRoutes.Group("/network_region")
			{
				logger.Debug("注册网络区域路由")
				policyNetworkRegion.POST("", common.ProxyCloudRequest)
				policyNetworkRegion.PUT("/:network_region_code", common.ProxyCloudRequest)    // 更新网络区域
				policyNetworkRegion.DELETE("/:network_region_code", common.ProxyCloudRequest) // 删除网络区域
				policyNetworkRegion.POST("/search", common.ProxyCloudRequest)                 // 搜索网络区域
			}

			policyProcessGroup := policyRoutes.Group("/process_group")
			{
				logger.Debug("注册策略流程组路由")
				policyProcessGroup.POST("", common.ProxyCloudRequest)                       // 创建策略流程组
				policyProcessGroup.PUT("/:process_group_code", common.ProxyCloudRequest)    // 更新策略流程组
				policyProcessGroup.DELETE("/:process_group_code", common.ProxyCloudRequest) // 删除策略流程组
				policyProcessGroup.POST("/search", common.ProxyCloudRequest)                // 搜索策略流程组
			}

			// 策略时间组相关路由
			policyTimeGroup := policyRoutes.Group("/time_group")
			{
				logger.Debug("注册策略时间组路由")
				policyTimeGroup.POST("", common.ProxyCloudRequest)             // 创建策略时间组
				policyTimeGroup.PUT("/:name_key", common.ProxyCloudRequest)    // 更新策略时间组
				policyTimeGroup.DELETE("/:name_key", common.ProxyCloudRequest) // 删除策略时间组
				policyTimeGroup.POST("/search", common.ProxyCloudRequest)      // 搜索策略时间组
			}
		}

		// 用户相关路由
		userRoutes := v1.Group("/org/user", middleware.JWT())
		{
			logger.Debug("注册用户相关路由")
			userRoutes.POST("/can_select", common.ProxyCloudRequest) // 搜索用户
			userRoutes.POST("", common.ProxyCloudRequest)            // 批量查询用户
			logger.Debug("已注册用户相关路由: POST /can_select")
		}

		userRoleRoutes := v1.Group("/org/user/roles", middleware.JWT())
		{
			logger.Debug("注册用户角色相关路由")
			userRoleRoutes.POST("/search_by_codes", common.ProxyCloudRequest) // 根据角色编码搜索用户
			userRoleRoutes.GET("/all", common.ProxyCloudRequest)              // 获取所有用户角色
			logger.Debug("已注册用户角色相关路由: POST /search_by_codes, GET /all")
		}

		// 用户组相关路由
		userGroupRoutes := v1.Group("/org/usergroup", middleware.JWT())
		{
			logger.Debug("注册用户组相关路由")
			userGroupRoutes.POST("", common.ProxyCloudRequest)                     // 创建用户组
			userGroupRoutes.PUT("/:group_code", common.ProxyCloudRequest)          // 更新用户组
			userGroupRoutes.DELETE("/:group_code", common.ProxyCloudRequest)       // 删除用户组
			userGroupRoutes.POST("/search", common.ProxyCloudRequest)              // 搜索用户组
			userGroupRoutes.GET("/:group_code", common.ProxyCloudRequest)          // 获取用户组详情
			userGroupRoutes.POST("/search_by_codes", common.ProxyCloudRequest)     // 根据用户组编码搜索用户组
			userGroupRoutes.GET("/user_code/:user_code", common.ProxyCloudRequest) // 根据用户编码搜索用户组
			userGroupRoutes.POST("/get_user_group", common.ProxyCloudRequest)      // 批量获取用户组
			logger.Debug("已注册用户组相关路由: POST /, PUT /:group_code, DELETE /:group_code, POST /search, GET /:group_code, POST /search_by_codes")
		}

		// 用户组关系相关路由
		userGroupRelationRoutes := v1.Group("/org/usergroup/:group_code/members", middleware.JWT())
		{
			logger.Debug("注册用户组关系相关路由")
			userGroupRelationRoutes.POST("", common.ProxyCloudRequest)                // 创建用户组关系
			userGroupRelationRoutes.DELETE("", common.ProxyCloudRequest)              // 删除用户组关系
			userGroupRelationRoutes.POST("/can_select", common.ProxyCloudRequest)     // 查询可选择成员
			userGroupRelationRoutes.POST("/selected", common.ProxyCloudRequest)       // 查询已选择成员
			userGroupRelationRoutes.POST("/search", common.ProxyCloudRequest)         // 搜索用户组关系
			userGroupRelationRoutes.GET("/export", common.ProxyCloudRequest)          // 搜索用户组关系
			userGroupRelationRoutes.POST("/import_members", common.ProxyCloudRequest) // 导入成员
			userGroupRelationRoutes.GET("/:user_code", common.ProxyCloudRequest)      // 获取用户组用户信息

			logger.Debug("已注册用户组关系相关路由: POST /, DELETE /, POST /can_select, POST /selected, POST /search, GET /export")
		}
		userGroupRelationNewRoutes := v1.Group("/org/usergroup/members", middleware.JWT())
		{
			userGroupRelationNewRoutes.POST("/batch_export", common.ProxyCloudRequest) // 搜索用户组关系
		}

		// 连接器集群相关路由（转到 connector-cluster-service 服务）
		connectorClusterRoutes := v1.Group("/connectors/clusters", middleware.JWT())
		{
			logger.Debug("注册连接器集群相关路由")
			connectorClusterRoutes.POST("", common.ProxyCloudConnectorClusterService)                         // 创建连接器集群
			connectorClusterRoutes.POST("/all", common.ProxyCloudConnectorClusterService)                     // 获取所有集群的精简信息
			connectorClusterRoutes.GET("/statistics", common.ProxyCloudConnectorClusterService)               // 获取连接器集群统计信息
			connectorClusterRoutes.POST("/search", common.ProxyCloudConnectorClusterService)                  // 搜索连接器集群
			connectorClusterRoutes.POST("/delete", common.ProxyCloudConnectorClusterService)                  // 批量删除连接器集群
			connectorClusterRoutes.POST("/cluster_names", common.ProxyCloudConnectorClusterService)           // 获取集群名称列表
			connectorClusterRoutes.GET("/:cluster_code", common.ProxyCloudConnectorClusterService)            // 获取连接器集群详情
			connectorClusterRoutes.PUT("/:cluster_code", common.ProxyCloudConnectorClusterService)            // 更新连接器集群
			connectorClusterRoutes.DELETE("/:cluster_code", common.ProxyCloudConnectorClusterService)         // 删除连接器集群
			connectorClusterRoutes.POST("/:cluster_code/restore", common.ProxyCloudConnectorClusterService)   // 恢复连接器集群
			connectorClusterRoutes.GET("/:cluster_code/connectors", common.ProxyCloudConnectorClusterService) // 获取集群内连接器列表
			logger.Debug("已注册连接器集群相关路由: POST /, GET /:cluster_code, POST /search, PUT /:cluster_code, DELETE /:cluster_code, POST /delete, POST /:cluster_code/restore, GET /:cluster_code/statistics, GET /:cluster_code/connectors, POST /cluster_names")
		}

		// 连接器相关路由（转到 connector-cluster-service 服务）
		connectorInfosRoutes := v1.Group("/connectors/infos", middleware.JWT())
		{
			logger.Debug("注册连接器相关路由")
			connectorInfosRoutes.POST("", common.ProxyCloudConnectorClusterService)                                    // 创建连接器
			connectorInfosRoutes.POST("/search", common.ProxyCloudConnectorClusterService)                             // 搜索连接器
			connectorInfosRoutes.DELETE("/:connector_code", common.ProxyCloudConnectorClusterService)                  // 删除连接器
			connectorInfosRoutes.PUT("/:connector_code/used_status", common.ProxyCloudConnectorClusterService)         // 更新连接器状态
			connectorInfosRoutes.PUT("/:connector_code/description", common.ProxyCloudConnectorClusterService)         // 更新连接器描述
			connectorInfosRoutes.PUT("/:connector_code/bandwidth_threshold", common.ProxyCloudConnectorClusterService) // 更新连接器带宽阈值
			connectorInfosRoutes.PUT("/used_status", common.ProxyCloudConnectorClusterService)                         // 批量更新连接器状态
			connectorInfosRoutes.POST("/delete", common.ProxyCloudConnectorClusterService)                             // 批量删除连接器
			// connectorInfosRoutes.PUT("/:connector_code", connectorHandler.UpdateConnector)              // 更新连接器
			// connectorInfosRoutes.GET("/:connector_code", connectorHandler.GetConnectorDetail)           // 获取连接器详情
			logger.Debug("已注册连接器相关路由: POST /, GET /:connector_code, POST /search, PUT /:connector_code, DELETE /:connector_code, PUT /:connector_code/status")

		}

		// 设备分组相关路由
		deviceGroupRoutes := v1.Group("/device/groups", middleware.JWT())
		{
			logger.Debug("注册设备分组相关路由")
			deviceGroupRoutes.POST("", common.ProxyCloudRequest)
			deviceGroupRoutes.PUT("/:group_code/name", common.ProxyCloudRequest)
			deviceGroupRoutes.PUT("/:group_code/condition", common.ProxyCloudRequest)
			deviceGroupRoutes.DELETE("/:group_code", common.ProxyCloudRequest)
			deviceGroupRoutes.GET("/:group_code", common.ProxyCloudRequest)
			deviceGroupRoutes.POST("/search", common.ProxyCloudRequest)
			logger.Debug("已注册设备分组相关路由: POST /, PUT /:group_code/name, PUT /:group_code/condition, DELETE /:group_code, GET /:group_code, POST /search")
		}

		// 合规检测的路由组
		strategyRouter := v1.Group("/strategy", middleware.JWT())
		{
			logger.Debug("注册合规检测相关路由")
			strategyRouter.Any("/*path", common.ProxySaseRequest)
		}

		//  漏洞修复路由组
		leakRepairRouter := v1.Group("/leak_repair", middleware.JWT())
		{
			logger.Debug("注册合规检测相关路由")
			leakRepairRouter.Any("/*path", common.ProxySaseRequest)
		}

		// 漏洞信息路由组
		leakRouter := v1.Group("/leak", middleware.JWT())
		{
			logger.Debug("注册漏洞信息相关路由")
			leakRouter.Any("/*path", common.ProxySaseRequest)
		}

		behaviorRouter := v1.Group("/behavior_control", middleware.JWT())
		{
			logger.Debug("注册行为日志信息相关路由")
			behaviorRouter.Any("/*path", common.ProxySaseRequest)
		}
		complianceCheckRouter := v1.Group("/compliance_check", middleware.JWT())
		{
			logger.Debug("注册行为日志信息相关路由")
			complianceCheckRouter.Any("/*path", common.ProxySaseRequest)
		}

		fireWall := v1.Group("/firewall", middleware.JWT())
		{
			logger.Debug("注册防火墙相关路由")
			fireWall.Any("/*path", common.ProxySaseRequest)
		}
		terminalRouter := v1.Group("/terminal", middleware.JWT())
		{
			logger.Debug("注册终端管理相关路由")
			terminalRouter.Any("/*path", common.ProxySaseRequest)
		}

		// 日志相关路由
		LogRoutes := v1.Group("/logs", middleware.JWT())
		{
			logger.Debug("注册ZTNA日志相关路由")
			LogRoutes.Any("/ztna", common.ProxyCloudRequest) // 搜索ZTNA日志
			LogRoutes.Any("/ztna/*path", common.ProxyCloudRequest)

			logger.Debug("注册合规日志相关路由")
			LogRoutes.Any("/compliance/*path", common.ProxySaseRequest)

			LogRoutes.Any("/peripheral_control", common.ProxySaseRequest)
			LogRoutes.Any("/peripheral_control/*path", common.ProxySaseRequest)

			LogRoutes.GET("/apply_permission", common.ProxyCloudRequest) // 搜索权限申请日志
			LogRoutes.POST("/proxy/audit", proxy_log.NewHandler().GetListProxyLog)

		}

		applyPermissionRoutes := v1.Group("/apply_permission", middleware.JWT())
		{
			logger.Debug("注册权限申请相关路由")
			applyPermissionRoutes.GET("/list", common.ProxyCloudRequest)      // 搜索权限申请
			applyPermissionRoutes.POST("/approval", common.ProxyCloudRequest) // 审批权限申请
			logger.Debug("已注册权限申请相关路由: POST /search")
		}

		// 计划任务相关路由
		planRouter := v1.Group("/plan", middleware.JWT())
		{
			planRouter.POST("", common.ProxySaseRequest)
			planRouter.Any("/*path", common.ProxySaseRequest)
		}

		// 分发软件记录相关路由
		distributeSoftwareRecord := v1.Group("/distribute_software", middleware.JWT())
		{
			distributeSoftwareRecord.Any("/*path", common.ProxySaseRequest)
		}

		appDiscoveryRouter := v1.Group("/app_discovery", middleware.JWT())
		{
			appDiscoveryRouter.POST("/list", common.ProxySaseRequest)
			appDiscoveryRouter.POST("/sub_domain", common.ProxySaseRequest)
			appDiscoveryRouter.POST("/update", common.ProxySaseRequest)
		}

		usbDevice := v1.Group("/usb_device", middleware.JWT())
		{
			usbDevice.Any("", common.ProxySaseRequest)
			usbDevice.Any("/*path", common.ProxySaseRequest)
		}

		distributeFileRouter := v1.Group("/distribute_file", middleware.JWT())
		{
			distributeFileRouter.Any("/*path", common.ProxySaseRequest)
		}

		sensitiveDataRouter := v1.Group("/sensitive_data", middleware.JWT())
		{
			sensitiveDataRouter.Any("/*path", common.ProxySaseRequest)
		}

		deployRoutes := v1.Group("/deploy", middleware.JWT())
		{
			logger.Debug("注册部署相关路由")
			deployRoutes.Any("/*path", common.ProxyCloudRequest)
		}

		deployInviteRoutes := v1.Group("/deploy_invite")
		{
			logger.Debug("注册部署邀请相关路由")
			deployInviteRoutes.GET("/:code", common.ProxyCloudRequest)
		}

		AdminContactRoutes := v1.Group("/admin_contact")
		{
			logger.Debug("注册管理员联系相关路由")
			AdminContactRoutes.GET("/:product_id", common.ProxyCloudRequest)
		}

		// 企业设置相关路由
		enterpriseSettingRoutes := v1.Group("/enterprise/settings", middleware.JWT())
		{
			logger.Debug("注册企业设置相关路由")
			enterpriseSettingRoutes.POST("", common.ProxyCloudRequest)      // 创建或更新企业设置
			enterpriseSettingRoutes.PUT("", common.ProxyCloudRequest)       // 更新企业设置
			enterpriseSettingRoutes.GET("", common.ProxyCloudRequest)       // 获取企业设置列表
			enterpriseSettingRoutes.GET("/:type", common.ProxyCloudRequest) // 获取特定类型的企业设置
			logger.Debug("已注册企业设置相关路由: POST /, PUT /, GET /, GET /:type")
		}

		// 软件相关路由
		softwareRouter := v1.Group("/software", middleware.JWT())
		{
			softwareRouter.POST("/list", common.ProxySaseRequest)
			softwareRouter.POST("/update_required", common.ProxySaseRequest)
			softwareRouter.POST("/installed_record/:soft_id", common.ProxySaseRequest)
			softwareRouter.POST("/installed_record_export", common.ProxySaseRequest)
			softwareRouter.Any("/category/*path", common.ProxyCloudSoftwareManagerClusterService)
			softwareRouter.Any("/relation/*path", common.ProxyCloudSoftwareManagerClusterService)
			softwareRouter.Any("/center/*path", common.ProxyCloudSoftwareManagerClusterService)
		}

		// 私有DNS解析相关路由
		privateDnsRoutes := v1.Group("/application/private_dns", middleware.JWT())
		{
			logger.Debug("注册私有DNS解析相关路由")
			privateDnsRoutes.POST("", common.ProxyCloudRequest)              // 创建私有DNS解析
			privateDnsRoutes.PUT("/:code", common.ProxyCloudRequest)         // 更新私有DNS解析
			privateDnsRoutes.DELETE("/:code", common.ProxyCloudRequest)      // 删除私有DNS解析
			privateDnsRoutes.GET("/:code", common.ProxyCloudRequest)         // 获取单个私有DNS解析
			privateDnsRoutes.GET("", common.ProxyCloudRequest)               // 获取私有DNS解析列表
			privateDnsRoutes.POST("/batch_delete", common.ProxyCloudRequest) // 批量删除私有DNS解析
			logger.Debug("已注册私有DNS解析相关路由: POST /, PUT /:code, DELETE /:code, GET /:code, GET /, POST /batch_delete")
		}
		// 应用SSO配置相关路由
		appSsoRoutes := v1.Group("/application/sso", middleware.JWT())
		{
			logger.Debug("注册应用SSO配置相关路由")
			appSsoRoutes.POST("", common.ProxyCloudRequest)              // 创建应用SSO配置
			appSsoRoutes.PUT("/:code", common.ProxyCloudRequest)         // 更新应用SSO配置
			appSsoRoutes.DELETE("/:code", common.ProxyCloudRequest)      // 删除应用SSO配置
			appSsoRoutes.GET("/:code", common.ProxyCloudRequest)         // 获取单个应用SSO配置
			appSsoRoutes.GET("", common.ProxyCloudRequest)               // 获取应用SSO配置列表
			appSsoRoutes.POST("/batch_delete", common.ProxyCloudRequest) // 批量删除应用SSO配置
			logger.Debug("已注册应用SSO配置相关路由: POST /, PUT /:code, DELETE /:code, GET /:code, GET /, POST /batch_delete")
		}

		statRouter := v1.Group("/stat", middleware.JWT())
		{
			statDeviceRoter := statRouter.Group("/device")
			statDeviceRoter.GET("/overview", common.ProxyEdrRequest)
			statDeviceRoter.GET("/os_version", common.ProxyEdrRequest)

			statMembersRoter := statRouter.Group("/members")
			statMembersRoter.GET("/active_count", common.ProxyCloudRequest)

			statComplianceChecRoter := statRouter.Group("/compliance")
			statComplianceChecRoter.POST("/statistics_terminals", common.ProxySaseRequest)

			statRouter.POST("/behavior", common.ProxySaseRequest)
			// 添加其他统计路由
		}

		// 指标统计相关路由
		indicatorRoutes := v1.Group("/indicator", middleware.JWT())
		{
			indicatorRoutes.GET("/status_list", common.ProxyCloudRequest)     // 获取指标状态列表
			indicatorRoutes.GET("/agg_status", common.ProxyCloudRequest)      // 聚合指标状态
			indicatorRoutes.GET("/top_count_list", common.ProxyCloudRequest)  // 获取Top计数列表
			indicatorRoutes.GET("/top_status_list", common.ProxyCloudRequest) // 获取Top状态列表
			indicatorRoutes.GET("/trend_list", common.ProxyCloudRequest)      // 获取趋势列表
			logger.Debug("已注册指标统计相关路由: GET /status_list, GET /agg_status, GET /top_count_list, GET /top_status_list, GET /trend_list")
		}

		// 策略动态因子相关路由
		policyFactorDynamicRoutes := v1.Group("/policy/policy_factor_dynamic", middleware.JWT())
		{
			logger.Debug("注册策略动态因子相关路由")
			policyFactorDynamicRoutes.PUT("", common.ProxyCloudRequest)       // 更新策略动态因子
			policyFactorDynamicRoutes.GET("/:type", common.ProxyCloudRequest) // 获取策略动态因子
			logger.Debug("已注册策略动态因子相关路由: PUT /, POST /search")
		}
		// 这里可以添加更多的路由组...
	}

	logger.Info("API路由注册完成")
}
