package verify_code

import (
	"context"
	"fmt"
	"sase-client-info/internal/common"
	"sase-client-info/internal/common/consts"
	"sase-client-info/internal/common/errors"
	"sase-client-info/internal/config"
	"sase-client-info/internal/dto"
	"sase-client-info/internal/model"
	"sase-client-info/internal/resource"
	"sase-client-info/internal/util"
	"sase-client-info/pkg/logger"
	"sase-client-info/pkg/utils/helper"
	"sase-client-info/pkg/utils/rmemail"
	"strconv"
	"strings"
	"time"

	"github.com/dromara/carbon/v2"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	smscommon "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"
	smserrors "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/errors"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/profile"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/regions"
	sms "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/sms/v20210111"
)

// Handler 策略处理器
type Handler struct{}

// NewHandler 创建策略处理器
func NewHandler() *Handler {
	return &Handler{}
}

func (h *Handler) IsNeedVerify(c *gin.Context) {

	var req dto.VerifyCodeIsNeedVerifyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Errorf("参数错误: %v", err)
		common.ResponseResult(c, errors.ParamsError)
		return
	}

	res, _ := model.VerifyCodeRepo.IsNeedVerifyInternal(c, &req)

	common.ResponseResult(c, res)
}

func (h *Handler) Send(c *gin.Context) {
	productId := c.GetString("product_id")
	phoneEmail := c.GetString("ciphertext")

	var req dto.VerifyCodeSendRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Errorf("发送验证码请求参数失败: %v", err)
		common.ResponseResult(c, errors.ParamsError)
		return
	}

	if _, ok := consts.VerifyCodeSourceMap[req.Source]; !ok {
		common.ResponseResult(c, errors.ParamsError)
		return
	}

	var email, phone string
	if strings.Contains(phoneEmail, "@") {
		email = phoneEmail
	} else {
		phone = phoneEmail
	}

	VerifyCodeIntervalKey := fmt.Sprintf(consts.VerifyCodeInterval, productId, phoneEmail, req.Source)
	VerifyCodeValidTimeKey := fmt.Sprintf(consts.VerifyCodeValidTime, productId, phoneEmail, req.Source)
	VerifyCodeTimesDayForPhoneEmailKey := fmt.Sprintf(consts.VerifyCodeTimesDayForPhoneEmail, productId, phoneEmail)
	VerifyCodeTimesDayForIpKey := fmt.Sprintf(consts.VerifyCodeTimesDayForIp, productId, strings.ReplaceAll(helper.GetClientIp(c), ".", "_"))

	redisTimes, _ := resource.Redisclient.Get(context.Background(), VerifyCodeTimesDayForPhoneEmailKey).Result()
	redisTimesDayNum, _ := strconv.ParseInt(redisTimes, 10, 64)
	if redisTimesDayNum >= int64(config.Get().VerifyCode.VerificationCodeTimesDay) {
		common.ResponseResult(c, errors.VerifyCodeTimeDayError)
		return
	}

	redisTimesDayForIp, _ := resource.Redisclient.Get(context.Background(), VerifyCodeTimesDayForIpKey).Result()
	redisTimesDayForIpNum, _ := strconv.ParseInt(redisTimesDayForIp, 10, 64)
	if redisTimesDayForIpNum >= int64(config.Get().VerifyCode.VerificationCodeTimesDayForIp) {
		common.ResponseResult(c, errors.VerifyCodeTimeDayForIpError)
		return
	}

	captcha, err := helper.CreateCaptcha(6)
	if err != nil {
		common.ResponseResult(c, errors.VerifyCodeCreateError)
		return
	}

	redisVal, _ := resource.Redisclient.Get(context.Background(), VerifyCodeIntervalKey).Result()
	if redisVal != "" {
		common.ResponseResult(c, errors.VerifyCodeIntervalError)
		return
	}

	timesNum, err := resource.Redisclient.Incr(context.Background(), VerifyCodeTimesDayForPhoneEmailKey).Result()
	logger.Infof("set redis times phone key: %s val:%v error:%v", VerifyCodeTimesDayForPhoneEmailKey, timesNum, err)

	ipNum, err := resource.Redisclient.Incr(context.Background(), VerifyCodeTimesDayForIpKey).Result()
	logger.Infof("set redis times ip key: %s val:%v error:%v", VerifyCodeTimesDayForIpKey, ipNum, err)

	if timesNum == 1 {
		ok, err := resource.Redisclient.ExpireAt(context.Background(), VerifyCodeTimesDayForPhoneEmailKey, carbon.Now().AddDays(1).StdTime()).Result()
		logger.Infof("set redis expire time phone key: %s res:%v error:%v", VerifyCodeTimesDayForPhoneEmailKey, ok, err)
	}

	if ipNum == 1 {
		ok, err := resource.Redisclient.ExpireAt(context.Background(), VerifyCodeTimesDayForIpKey, carbon.Now().AddDays(1).StdTime()).Result()
		logger.Infof("set redis expire time ip key: %s res:%v error:%v", VerifyCodeTimesDayForIpKey, ok, err)
	}

	_, err = resource.Redisclient.Set(context.Background(), VerifyCodeIntervalKey, captcha, time.Duration(config.Get().VerifyCode.VerificationCodeInterval)*time.Second).Result()
	if err != nil {
		logger.Errorf("sms set key1: %s error:%v", VerifyCodeIntervalKey, err)
		common.ResponseResult(c, errors.VerifyCodeSetTimesError)
		return
	}

	_, err = resource.Redisclient.Set(context.Background(), VerifyCodeValidTimeKey, captcha, time.Duration(config.Get().VerifyCode.VerificationCodeValidTime)*time.Minute).Result()

	if err != nil {
		logger.Errorf("sms set key2: %s error:%v", VerifyCodeValidTimeKey, err)
		common.ResponseResult(c, errors.VerifyCodeSetValidCodeError)
		return
	}

	if email != "" {
		err = h.sendEmail(email, captcha)
		if err != nil {
			common.ResponseResult(c, errors.VerifyCodeSetValidCodeError)
			return
		}
	} else {
		err = h.sendPhone(phone, captcha)
		if err != nil {
			common.ResponseResult(c, errors.VerifyCodeSetValidCodeError)
			return
		}
	}

	common.ResponseResult(c, errors.Success)
}
func (h *Handler) sendEmail(email, captcha string) error {
	var emails []string
	emails = append(emails, email)
	content := fmt.Sprintf(consts.EmailVerificationCodeTmp, email, captcha)

	if config.Get().Server.CompanyCode == consts.CompanyQax {
		tsMlmConfig := config.Get().TsMlmConfig
		err := util.SendTsEmail(tsMlmConfig.EmailAddress, tsMlmConfig.EmailToken, tsMlmConfig.EmailFrom, content, config.Get().Server.CompanyBrandName+" Verification Code", emails, nil, nil, tsMlmConfig.InsecureSkipVerify)
		return err
	}

	emailConfig := rmemail.RmEmailConfig{
		Port:      config.Get().Email.Port,
		Host:      config.Get().Email.Host,
		User:      config.Get().Email.User,
		Password:  config.Get().Email.Password,
		AliasName: config.Get().Email.AliasName,
	}
	rmEmail := rmemail.NewRmEmail(emailConfig)
	err := rmEmail.SendEmail(content, config.Get().Server.CompanyBrandName+" Verification Code", emails, nil, nil)
	if err != nil {
		logger.Errorf("发送验证码邮件失败: %v", err)
		return err
	}
	return nil
}

func (h *Handler) sendPhone(phone, captcha string) error {
	var err error

	if config.Get().Server.CompanyCode == consts.CompanyQax {
		tsMlmConfig := config.Get().TsMlmConfig
		msg := fmt.Sprintf("%s是您的验证码，有效期5分钟，您正在验证身份，请勿将验证码转发给他人，验证码泄露可能导致账号被盗用。", captcha)
		err = util.SendTsSms(tsMlmConfig.SmsAddress, tsMlmConfig.Token, msg, tsMlmConfig.User, []string{phone}, tsMlmConfig.InsecureSkipVerify)
		return err
	}

	speedPhone := fmt.Sprintf("+86%s", phone)
	phones := []string{speedPhone}

	//验证码有效时长
	codeValidTime := cast.ToString(config.Get().VerifyCode.VerificationCodeValidTime)
	tParamSet := []string{captcha, codeValidTime}

	credential := smscommon.NewCredential(config.Get().VerifyCode.SmsSecretId, config.Get().VerifyCode.SmsSecretKey)
	cpf := profile.NewClientProfile()
	cpf.HttpProfile.ReqMethod = "POST"
	cpf.HttpProfile.Endpoint = "sms.tencentcloudapi.com"
	cpf.SignMethod = "HmacSHA1"

	client, _ := sms.NewClient(credential, regions.Beijing, profile.NewClientProfile())
	request := sms.NewSendSmsRequest()
	request.SmsSdkAppId = smscommon.StringPtr(config.Get().VerifyCode.SmsSdkAppid)
	request.SignName = smscommon.StringPtr(config.Get().VerifyCode.SmsSignName)
	request.TemplateId = smscommon.StringPtr(config.Get().VerifyCode.SmsTplId)
	request.TemplateParamSet = smscommon.StringPtrs(tParamSet)
	request.PhoneNumberSet = smscommon.StringPtrs(phones)
	request.SessionContext = smscommon.StringPtr("")
	request.ExtendCode = smscommon.StringPtr("")
	request.SenderId = smscommon.StringPtr("")

	res, err := client.SendSms(request)
	// 处理异常
	if _, ok := err.(*smserrors.TencentCloudSDKError); ok {
		return err
	}
	// 非SDK异常，直接失败。实际代码中可以加入其他的处理。
	if err != nil {
		return err
	}

	if len(res.Response.SendStatusSet) > 0 {
		code := *res.Response.SendStatusSet[0].Code
		if strings.ToLower(code) != "ok" {
			return fmt.Errorf("send sms fail")
		}
	}

	return nil
}
