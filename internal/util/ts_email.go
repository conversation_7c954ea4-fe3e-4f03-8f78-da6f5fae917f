package util

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/url"
)

type EmailData struct {
	From    string   `json:"from"`
	To      []string `json:"to"`
	Cc      []string `json:"cc"`
	Bcc     []string `json:"bcc"`
	Subject string   `json:"subject"`
	Body    string   `json:"body"`
	Format  string   `json:"format"`
}

type EmailMsg struct {
	User        string     `json:"user"`
	Priority    int        `json:"priority"`
	UseTemplate bool       `json:"useTemplate"`
	EmailData   *EmailData `json:"emailData"`
}

func SendTsEmail(address string, token string, fromEmail string, msg string, subject string, toEmail []string, cc []string, bcc []string, insecureSkipVerify bool) error {

	emailData := &EmailData{
		From:    fromEmail,
		Body:    msg,
		To:      toEmail,
		Cc:      cc,
		Bcc:     bcc,
		Subject: subject,
		Format:  "html",
	}

	emailDataJsonByte, _ := json.Marshal(emailData)
	params := url.Values{}
	params.Add("token", token)
	params.Add("mail", string(emailDataJsonByte))
	params.Add("priority", "1")
	params.Add("useTemplate", "false")

	address = fmt.Sprintf("%s?%s", address, params.Encode())
	fmt.Println("------------", address)
	resByte, err := curlRequest(address, "POST", []byte{}, map[string]string{}, insecureSkipVerify)
	if err != nil {
		return err
	}

	var reponse TsRespone
	if err = json.Unmarshal(resByte, &reponse); err != nil {
		return err
	}

	if reponse.Status != 1 {
		return errors.New(reponse.Msg)
	}

	return nil
}
