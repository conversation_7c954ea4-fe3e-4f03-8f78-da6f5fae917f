package util

import (
	"bytes"
	"context"
	"crypto/tls"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"time"
)

type SmsData struct {
	Phone  []string               `json:"phone"`
	Params map[string]interface{} `json:"params"`
	Msg    string                 `json:"msg"`
}

type SmsMsg struct {
	User        string   `json:"user"`
	Priority    int      `json:"priority"`
	UseTemplate bool     `json:"useTemplate"`
	SmsData     *SmsData `json:"smsData"`
}

type TsRespone struct {
	Status int    `json:"status"`
	Msg    string `json:"msg"`
}

func SendTsSms(address, token, msg, user string, phones []string, insecureSkipVerify bool) error {

	smsMsg := &SmsMsg{
		User:        user,
		Priority:    1,
		UseTemplate: false,
	}
	smsMsg.SmsData = &SmsData{
		Phone: phones,
		Msg:   msg,
	}

	reqData, _ := json.Marshal(smsMsg)

	address = fmt.Sprintf(address+"?%s", "token="+token)

	resByte, err := curlRequest(address, "POST", reqData, map[string]string{}, insecureSkipVerify)
	if err != nil {
		return err
	}

	var reponse TsRespone
	if err = json.Unmarshal(resByte, &reponse); err != nil {
		return err
	}

	if reponse.Status != 1 {
		return errors.New(reponse.Msg)
	}

	return nil
}

func curlRequest(apiUrl string, requestType string, data []byte, header map[string]string, insecureSkipVerify bool) ([]byte, error) {
	buffer := bytes.NewBuffer(data)

	http.DefaultTransport.(*http.Transport).TLSClientConfig = &tls.Config{InsecureSkipVerify: insecureSkipVerify}

	request, err := http.NewRequest(requestType, apiUrl, buffer)

	if err != nil {
		return []byte(""), err
	}

	if len(header) > 0 {
		for k, val := range header {
			request.Header.Add(k, val) //添加请求头
		}
	} else {
		if requestType != "GET" {
			request.Header.Set("Content-Type", "application/json;charset=UTF-8") //添加请求头
		}
	}

	//创建客户端
	client := http.Client{
		Timeout: 30 * time.Second,
	}

	resp, err := client.Do(request.WithContext(context.TODO())) //发送请求
	if err != nil {
		return []byte(""), err
	}

	defer resp.Body.Close()

	respBytes, err := io.ReadAll(resp.Body)
	return respBytes, err
}
