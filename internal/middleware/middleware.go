package middleware

import (
	"bytes"
	"compress/gzip"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"sase-client-info/internal/common"
	"sase-client-info/internal/common/errors"
	"sase-client-info/internal/config"
	"sase-client-info/internal/resource"
	"sase-client-info/pkg/auth/jwt"
	"sase-client-info/pkg/logger"
	"sase-client-info/pkg/utils/helper"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/spf13/cast"
)

var (
	cfg = config.Get()
)

// RequestID 添加请求ID中间件
func RequestID() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 尝试从请求头获取请求ID
		requestID := c.GetHeader("X-Request-ID")
		if requestID == "" {
			// 生成新的请求ID
			requestID = uuid.New().String()
		}

		// 将请求ID设置到上下文和响应头中
		c.Set("RequestID", requestID)
		c.Header("X-Request-ID", requestID)

		c.Next()
	}
}

// Logger 日志中间件
func Logger() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 开始时间
		start := time.Now()
		path := c.Request.URL.Path
		method := c.Request.Method
		requestID, _ := c.Get("RequestID")

		// 处理请求
		c.Next()

		// 结束时间
		end := time.Now()
		latency := end.Sub(start)
		statusCode := c.Writer.Status()
		clientIP := c.ClientIP()

		// 日志格式
		logFormat := "[GIN] %v | %3d | %13v | %15s | %-7s %s | %s\n"
		fmt.Printf(logFormat,
			end.Format("2006/01/02 - 15:04:05"),
			statusCode,
			latency,
			clientIP,
			method,
			path,
			requestID,
		)
	}
}

// Recovery 恢复中间件
func Recovery() gin.HandlerFunc {
	return gin.Recovery()
}

// Cors 跨域中间件
func Cors() gin.HandlerFunc {
	return func(c *gin.Context) {
		method := c.Request.Method               //请求方法
		origin := c.Request.Header.Get("Origin") // 请求头部

		if origin != "" {
			if len(cfg.Server.AccessControlAllowOrigin) == 1 {
				c.Header("Access-Control-Allow-Origin", "*")
			} else {
				for _, o := range cfg.Server.AccessControlAllowOrigin {
					if origin == o {
						c.Header("Access-Control-Allow-Origin", o)
						break
					}
				}
			}

			// 这是允许访问所有域
			c.Header("Access-Control-Allow-Methods", "POST, GET, OPTIONS, PUT, DELETE, UPDATE") // 服务器支持的所有跨域请求的方法,为了避免浏览次请求的多次'预检'请求
			// header的类型
			c.Header("Access-Control-Allow-Headers", "Authorization, Content-Length, X-CSRF-Token, Token,session, X_Requested_With, Accept, Origin, Host, Connection, Accept-Encoding, Accept-Language, DNT, X-CustomHeader, Keep-Alive, User-Agent, X-Requested-With, If-Modified-Since, Cache-Control, Content-Type, Pragma, Sign, X-Language")
			// 允许跨域设置                                                                                                      可以返回其他子段
			c.Header("Access-Control-Expose-Headers", "Content-Length, Access-Control-Allow-Origin, Access-Control-Allow-Headers,Cache-Control,Content-Language,Content-Type,Expires,Last-Modified,Pragma,FooBar") // 跨域关键设置 让浏览器可以解析
			c.Header("Access-Control-Max-Age", "172800")                                                                                                                                                           //缓存请求信息 单位为秒
			c.Header("Access-Control-Allow-Credentials", "false")                                                                                                                                                  //跨域请求是否需要带cookie信息 默认设置为true
		}

		// 放行所有OPTIONS方法
		if method == "OPTIONS" {
			c.JSON(http.StatusOK, "Options Request!")
		}
	}
}

func JWT() gin.HandlerFunc {
	return func(c *gin.Context) {
		t := time.Now()
		var err error
		var claims *jwt.Claims
		var info map[string]interface{}
		token := c.Request.Header.Get("Authorization")
		if token == "" {
			logger.Errorf("error from middleware 1 ip:%v path:%v Authorization:%v", helper.GetClientIp(c), c.Request.RequestURI, token)
			// 非登录状态
			common.ResponseResult(c, errors.UserJWTCheckFail)
			c.Abort()
			return
		} else {
			claims, info, err = resource.Jwt.Parse(token)
			if err != nil {
				if err == jwt.ErrExpiredToken {
					logger.Errorf("error from middleware 2 ip:%v path:%v Authorization:%v ", helper.GetClientIp(c), c.Request.RequestURI, token)
					common.ResponseResult(c, errors.UserJWTExpired)
				} else if err == jwt.ErrDeletedToken {
					logger.Errorf("error from middleware 3 ip:%v path:%v Authorization:%v ", helper.GetClientIp(c), c.Request.RequestURI, token)
					common.ResponseResult(c, errors.UserJWTDeleted)
				} else {
					logger.Errorf("error from middleware 4 ip:%v path:%v Authorization:%v", helper.GetClientIp(c), c.Request.RequestURI, token)
					common.ResponseResult(c, errors.UserJWTCheckFail)
				}
				c.Abort()
				return
			}
			if claims.ProductId == "" || claims.UserId == "" || cast.ToString(info["dcs_api"]) == "" {
				logger.Errorf("error from middleware 5 ip:%v path:%v Authorization:%v claims.ProductId %v claims.UserId %v uid %v dcs_api %v", helper.GetClientIp(c), c.Request.RequestURI, token, claims.ProductId, claims.UserId, cast.ToString(info["dcs_api"]))
				common.ResponseResult(c, errors.UserJWTCheckFail)
				c.Abort()
				return
			}
			c.Set("channel", claims.Channel)
			c.Set("user_id", claims.UserId)
			c.Set("user_name", claims.UserName)
			c.Set("product_id", claims.ProductId)
			c.Set("dcs_api", info["dcs_api"])
			c.Set("ciphertext", info["ciphertext"])
			c.Set("passport_user_info", info["passport_user_info"])
		}

		logger.Infof("info from jwt middleware time:%v", time.Since(t).Milliseconds())

	}
}

func DevTest() gin.HandlerFunc {
	return func(c *gin.Context) {
		devTest := config.Get().DevTest
		if devTest.Switch {
			c.Set("user_id", devTest.UserId)
			c.Set("user_name", devTest.UserName)
			c.Set("product_id", devTest.SaseProductId)
			c.Set("edr_dcs_api", devTest.EdrDcsApi)
			c.Set("sase_dcs_api", devTest.SaseDcsApi)
			c.Set("ciphertext", devTest.Ciphertext)
		}
	}

}
func AccessLogger(ctx *gin.Context) {

	if _, ok := helper.AuditMap[ctx.GetString("path")]; ok {
		bodyMap := make(map[string]interface{})

		bodyByte, err := ctx.GetRawData()
		if err == nil {
			_ = json.Unmarshal(bodyByte, &bodyMap)
		}
		ctx.Request.Body = io.NopCloser(bytes.NewBuffer(bodyByte))
		blw := &bodyLogWriter{Body: bytes.NewBufferString(""), ResponseWriter: ctx.Writer}

		ctx.Writer = blw

		ctx.Next()

		userId := ctx.GetString("user_id")
		userName := ctx.GetString("user_name")
		orgName := ctx.GetString("product_id")

		var resp common.Response

		err = json.Unmarshal(blw.Body.Bytes(), &resp)
		if err != nil {
			if strings.Contains(ctx.GetHeader("Accept-Encoding"), "gzip") {
				gzReader, err := gzip.NewReader(blw.Body)
				if err != nil {
					logger.Errorf("failed to unzip data: %s", err)
				} else {
					defer gzReader.Close()
					bytesData, err := io.ReadAll(gzReader)
					if err != nil {
						logger.Errorf("fail to ReadAll error: %s", err)
					} else {
						err = json.Unmarshal(bytesData, &resp)
						if err != nil {
							logger.Errorf("fail to Unmarshal error: %s", err)
						}
					}

				}

			}
		}
		if resp.Error == 0 {
			access := helper.New(helper.WithPath(ctx.Request.URL.Path), helper.WithIp(helper.GetClientIp(ctx)), helper.WithViewType(1),
				helper.WithRequest(bodyMap), helper.WithOrgName(orgName),
				helper.WithUserName(userName), helper.WithUId(userId), helper.WithPath(ctx.GetString("path")),
				helper.WithFrom(ctx.GetString("dcs_api")), helper.WithHeader(map[string]interface{}{
					"X-UserId":   userId,
					"X-UserName": userName,
					"X-OrgName":  orgName,
					"X-From":     "console",
				}),
				helper.WithResponse(resp.Data), helper.WithTime(time.Now().Unix()))
			//考虑到同一路由，根据参数不同，功能不同做的处理
			if access.Path == "/api/v1/detection/alarms/events_log/alarm_list" {
				if v, ok := bodyMap["is_export"]; !ok || cast.ToInt(v) != 1 {
					return
				}
			}

			helper.BuildAccess(ctx, access)

			resource.LogRecorder.Access(access)
		}
	} else {
		ctx.Next()
	}

}

type bodyLogWriter struct {
	gin.ResponseWriter
	Body *bytes.Buffer
}

func (w *bodyLogWriter) Write(b []byte) (int, error) {
	w.Body.Write(b)
	return w.ResponseWriter.Write(b)
}
