package verify_code

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"sase-client-info/internal/common/consts"
	"sase-client-info/internal/common/errors"
	"sase-client-info/internal/dto"
	"sase-client-info/internal/resource"
	"sase-client-info/pkg/utils/helper"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

type Repository interface {
	CheckVerifyCode(ctx *gin.Context, verifyCodeSource string) error
	IsNeedVerifyInternal(c *gin.Context, req *dto.VerifyCodeIsNeedVerifyRequest) (*dto.VerifyCodeIsNeedVerifyResponse, error)
}

// MongoRepository MongoDB实现
type repository struct {
}

// NewRepository 创建新的仓库实例
func NewRepository() Repository {
	return &repository{}
}

func (r *repository) IsNeedVerifyInternal(c *gin.Context, req *dto.VerifyCodeIsNeedVerifyRequest) (*dto.VerifyCodeIsNeedVerifyResponse, error) {
	isNeed := true
	var intervalInt64 int64
	res := &dto.VerifyCodeIsNeedVerifyResponse{
		PhoneEmail:       c.GetString("ciphertext"),
		IsNeedVerifyCode: isNeed,
		I:                intervalInt64,
	}
	return res, nil
}

func (r *repository) CheckVerifyCode(ctx *gin.Context, verifyCodeSource string) error {

	channel := ctx.GetString("channel")
	uid := ctx.GetString("user_id")
	orgName := ctx.GetString("product_id")
	fmt.Println("----------------", channel, uid, orgName)
	if channel == consts.ChannelRooma || channel == consts.ChannelTianShouZhPure || channel == consts.ChannelTianShou || channel == consts.ChannelRmSase {
		// 判断是否需要验证
		isNeedData, _ := r.IsNeedVerifyInternal(ctx, &dto.VerifyCodeIsNeedVerifyRequest{})
		phoneEmail := ctx.GetString("ciphertext")
		if isNeedData.IsNeedVerifyCode {
			bodyMap := make(map[string]interface{})

			bodyByte, err := ctx.GetRawData()
			if err == nil {
				_ = json.Unmarshal(bodyByte, &bodyMap)
			}

			ctx.Request.Body = io.NopCloser(bytes.NewBuffer(bodyByte))

			if verifyCodeSource == consts.VerifyCodeSource4 {
				instructionName := cast.ToString(bodyMap["instruction_name"])
				instructionNameList := []string{consts.KillProcess,
					consts.BatchKillProcess,
					consts.QuarantineFile,
					consts.BatchQuarantineFile,
					consts.QuarantineNetwork,
					consts.RecoverNetwork,
					consts.GetSuspiciousFile,
					consts.ProcessDump}
				if !helper.InArray(instructionName, instructionNameList) {
					return nil
				}
			}

			var verifyCode string
			if verifyCodeSource == consts.VerifyCodeSource5 {
				showPassword := cast.ToInt(ctx.Query("show_password"))
				if showPassword != 1 {
					return nil
				}
				verifyCode = ctx.Query("verify_code")
			} else {
				verifyCode = cast.ToString(bodyMap["verify_code"])
			}

			if verifyCode == "" {
				return errors.VerifyCodeNotEmpty
			}

			VerifyCodeValidTimeKey := fmt.Sprintf(consts.VerifyCodeValidTime, orgName, phoneEmail, verifyCodeSource)
			VerifyCodeIntervalKey := fmt.Sprintf(consts.VerifyCodeInterval, orgName, phoneEmail, verifyCodeSource)

			verificationCode, _ := resource.Redisclient.Get(context.Background(), VerifyCodeValidTimeKey).Result()

			if verificationCode != verifyCode {
				return errors.VerifyCodeValidationFailed
			}

			defer func() {
				resource.Redisclient.Del(context.Background(), VerifyCodeValidTimeKey)
				resource.Redisclient.Del(context.Background(), VerifyCodeIntervalKey)
			}()

			verIfyCodeLastTimeKey := fmt.Sprintf(consts.VerIfyCodeLastTime, orgName, uid)
			resource.Redisclient.Set(context.Background(), verIfyCodeLastTimeKey, verifyCode, time.Duration(isNeedData.I)*time.Second)
		}
	}
	return nil
}
