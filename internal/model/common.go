package model

import (
	"sase-client-info/internal/model/logs"
	"sase-client-info/internal/model/office_setting"
	"sase-client-info/internal/model/passport"
	"sase-client-info/internal/model/verify_code"
	"sase-client-info/pkg/logger"
)

var (

	// OfficeSettingRepo 全局办公设置仓库实例
	OfficeSettingRepo office_setting.Repository
	ProxyLogRepo      logs.Repository
	VerifyCodeRepo    verify_code.Repository
	PassportRepo      passport.Repository
)

// Init 初始化所有model实例
func Init() {
	logger.Info("初始化Model层公共实例...")

	// 初始化办公设置仓库
	OfficeSettingRepo = office_setting.NewRepository()
	logger.Debug("已初始化办公设置仓库")

	logger.Info("Model层公共实例初始化完成")

	ProxyLogRepo = logs.NewRepository()

	VerifyCodeRepo = verify_code.NewRepository()

	PassportRepo = passport.NewRepository()
}
