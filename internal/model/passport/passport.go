package passport

import (
	"encoding/json"
	"fmt"
	"sase-client-info/internal/common/errors"
	"sase-client-info/internal/config"
	"sase-client-info/internal/dto"
	"sase-client-info/pkg/logger"
	"sase-client-info/pkg/utils/helper"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/spf13/cast"
)

type Repository interface {
	GetUserAllOrgAppList(userInfo map[string]interface{}) ([]byte, error)
	GetOrgAppInfoByOrgName(userInfo map[string]interface{}, req *dto.ExternalGetOrgAppInfoByOrgName) (*dto.ExternalGetOrgAppInfoByOrgNameResponse, error)
	ReceiveInvitationList(userInfo map[string]interface{}, req *dto.ExternalReceiveInvitationList) ([]byte, error)
	UpdateReceiveInvitationStatus(userInfo map[string]interface{}, req *dto.ExternalUpdateReceiveInvitationStatus) ([]byte, error)
	GetOrgMemberData(userInfo map[string]interface{}, req *dto.ExternalGetOrgMemberData) ([]byte, error)
	InsertMember(userInfo map[string]interface{}, req *dto.ExternalInsertMember) ([]byte, error)
	RemoveOrgUser(userInfo map[string]interface{}, req *dto.ExternalRemoveOrgUser) ([]byte, error)
	GetProductRoles(userInfo map[string]interface{}, req *dto.ExternalGetProductRoles) ([]byte, error)
	GetUserOrgApp(userInfo map[string]interface{}, req *dto.ExternalGetUserOrgApp) ([]byte, error)
	UpdateUserOrgAppRole(userInfo map[string]interface{}, req *dto.ExternalUpdateUserOrgAppRole) ([]byte, error)
	InvitationList(userInfo map[string]interface{}, req *dto.ExternalInvitationList) ([]byte, error)
	UpdateInvitationStatus(userInfo map[string]interface{}, req *dto.ExternalUpdateInvitationStatus) ([]byte, error)
	GetMessageList(userInfo map[string]interface{}, req *dto.ExternalGetMessageList) ([]byte, error)
	GetUnreadNum(userInfo map[string]interface{}, req *dto.ExternalGetUnreadNum) ([]byte, error)
	MarkRead(userInfo map[string]interface{}, req *dto.ExternalMarkRead) ([]byte, error)
	MessageDetail(userInfo map[string]interface{}, req *dto.ExternalMessageDetail) ([]byte, error)
	SwitchOrgName(userInfo map[string]interface{}, req *dto.ExternalSwitchOrgName) ([]byte, error)
}

type repository struct{}

// NewRepository 创建新的仓库实例
func NewRepository() Repository {
	return &repository{}
}

func (p *repository) GetUserAllOrgAppList(userInfo map[string]interface{}) ([]byte, error) {
	requestUrl := strings.TrimSuffix(config.Get().Passport.PassportBackendUrl, "/") + "/external/get_user_org_app_list_by_user_id"
	currentTime := cast.ToString(time.Now().Unix())
	appid := config.Get().Passport.Appid
	appSecret := config.Get().Passport.AppSecret
	uuid := uuid.New().String()

	sign := helper.GeneratorPassportSign(appid, appSecret, uuid, currentTime)
	requestData := map[string]interface{}{
		"sign":      sign,
		"appid":     cast.ToInt(appid),
		"time":      cast.ToInt(currentTime),
		"uuid":      uuid,
		"user_info": userInfo,
		"callback":  config.Get().Passport.Callback,
	}

	jsonStr, _ := json.Marshal(requestData)

	respByte, err := helper.CurlRequest(requestUrl, "POST", jsonStr, map[string]string{}, config.Get().Settings.InsecureSkipVerify)
	if err != nil {
		logger.Errorf("error from service passport request get_user_org_app_list_by_user_id: %v %v %v", err, requestUrl, string(jsonStr))
		return nil, errors.ParamsError
	}

	return respByte, nil
}

func (p *repository) SwitchOrgName(userInfo map[string]interface{}, req *dto.ExternalSwitchOrgName) ([]byte, error) {
	requestUrl := strings.TrimSuffix(config.Get().Passport.PassportBackendUrl, "/") + "/external/switch_org_name"
	currentTime := cast.ToString(time.Now().Unix())
	appid := config.Get().Passport.Appid
	appSecret := config.Get().Passport.AppSecret
	uuid := uuid.New().String()

	sign := helper.GeneratorPassportSign(appid, appSecret, uuid, currentTime)
	requestData := map[string]interface{}{
		"sign":      sign,
		"appid":     cast.ToInt(appid),
		"time":      cast.ToInt(currentTime),
		"uuid":      uuid,
		"user_info": userInfo,
		"callback":  config.Get().Passport.Callback,
		"org_name":  req.ProductId,
	}

	fmt.Println("++++++++++++++++++++++++++++", requestData)

	jsonStr, _ := json.Marshal(requestData)

	respByte, err := helper.CurlRequest(requestUrl, "POST", jsonStr, map[string]string{}, config.Get().Settings.InsecureSkipVerify)
	if err != nil {
		logger.Errorf("error from service passport request switch_org_name: %v %v %v", err, requestUrl, string(jsonStr))
		return nil, errors.ParamsError
	}

	return respByte, nil
}

func (p *repository) GetOrgAppInfoByOrgName(userInfo map[string]interface{}, req *dto.ExternalGetOrgAppInfoByOrgName) (*dto.ExternalGetOrgAppInfoByOrgNameResponse, error) {
	requestUrl := strings.TrimSuffix(config.Get().Passport.PassportBackendUrl, "/") + "/external/get_org_app"
	currentTime := cast.ToString(time.Now().Unix())
	appid := config.Get().Passport.Appid
	appSecret := config.Get().Passport.AppSecret
	uuid := uuid.New().String()

	sign := helper.GeneratorPassportSign(appid, appSecret, uuid, currentTime)
	requestData := map[string]interface{}{
		"sign":      sign,
		"appid":     cast.ToInt(appid),
		"time":      cast.ToInt(currentTime),
		"uuid":      uuid,
		"user_info": userInfo,
		"callback":  config.Get().Passport.Callback,
		"org_name":  req.ProductId,
	}

	jsonStr, _ := json.Marshal(requestData)

	respByte, err := helper.CurlRequest(requestUrl, "POST", jsonStr, map[string]string{}, config.Get().Settings.InsecureSkipVerify)
	if err != nil {
		logger.Errorf("error from service passport request switch_org_name: %v %v %v", err, requestUrl, string(jsonStr))
		return nil, errors.ParamsError
	}

	type resStruct struct {
		Error   int                                         `json:"error"`
		Message string                                      `json:"message"`
		Data    *dto.ExternalGetOrgAppInfoByOrgNameResponse `json:"data"`
	}

	var res resStruct
	json.Unmarshal(respByte, &res)

	return res.Data, nil
}

func (p *repository) ReceiveInvitationList(userInfo map[string]interface{}, req *dto.ExternalReceiveInvitationList) ([]byte, error) {
	requestUrl := strings.TrimSuffix(config.Get().Passport.PassportBackendUrl, "/") + "/external/receive_invitation_list"
	currentTime := cast.ToString(time.Now().Unix())
	appid := config.Get().Passport.Appid
	appSecret := config.Get().Passport.AppSecret
	uuid := uuid.New().String()

	sign := helper.GeneratorPassportSign(appid, appSecret, uuid, currentTime)
	requestData := map[string]interface{}{
		"sign":      sign,
		"appid":     cast.ToInt(appid),
		"time":      cast.ToInt(currentTime),
		"uuid":      uuid,
		"user_info": userInfo,
		"callback":  config.Get().Passport.Callback,
		"page":      req.Page,
		"pagesize":  req.Limit,
	}

	jsonStr, _ := json.Marshal(requestData)

	respByte, err := helper.CurlRequest(requestUrl, "POST", jsonStr, map[string]string{}, config.Get().Settings.InsecureSkipVerify)
	if err != nil {
		logger.Errorf("error from service passport request receive_invitation_list: %v %v %v", err, requestUrl, string(jsonStr))
		return nil, errors.ParamsError
	}

	return respByte, nil
}

func (p *repository) UpdateReceiveInvitationStatus(userInfo map[string]interface{}, req *dto.ExternalUpdateReceiveInvitationStatus) ([]byte, error) {
	requestUrl := strings.TrimSuffix(config.Get().Passport.PassportBackendUrl, "/") + "/external/update_receive_invitation_status"
	currentTime := cast.ToString(time.Now().Unix())
	appid := config.Get().Passport.Appid
	appSecret := config.Get().Passport.AppSecret
	uuid := uuid.New().String()

	sign := helper.GeneratorPassportSign(appid, appSecret, uuid, currentTime)
	requestData := map[string]interface{}{
		"sign":      sign,
		"appid":     cast.ToInt(appid),
		"time":      cast.ToInt(currentTime),
		"uuid":      uuid,
		"user_info": userInfo,
		"callback":  config.Get().Passport.Callback,
		"status":    req.Status,
		"id":        req.Id,
	}

	jsonStr, _ := json.Marshal(requestData)

	respByte, err := helper.CurlRequest(requestUrl, "POST", jsonStr, map[string]string{}, config.Get().Settings.InsecureSkipVerify)
	if err != nil {
		logger.Errorf("error from service passport request update_receive_invitation_status: %v %v %v", err, requestUrl, string(jsonStr))
		return nil, errors.ParamsError
	}

	return respByte, nil
}

func (p *repository) GetOrgMemberData(userInfo map[string]interface{}, req *dto.ExternalGetOrgMemberData) ([]byte, error) {
	requestUrl := strings.TrimSuffix(config.Get().Passport.PassportBackendUrl, "/") + "/external/get_org_members"
	currentTime := cast.ToString(time.Now().Unix())
	appid := config.Get().Passport.Appid
	appSecret := config.Get().Passport.AppSecret
	uuid := uuid.New().String()

	sign := helper.GeneratorPassportSign(appid, appSecret, uuid, currentTime)
	requestData := map[string]interface{}{
		"sign":      sign,
		"appid":     cast.ToInt(appid),
		"time":      cast.ToInt(currentTime),
		"uuid":      uuid,
		"user_info": userInfo,
		"callback":  config.Get().Passport.Callback,
		"page":      req.Page,
		"pagesize":  req.Limit,
	}

	jsonStr, _ := json.Marshal(requestData)

	respByte, err := helper.CurlRequest(requestUrl, "POST", jsonStr, map[string]string{}, config.Get().Settings.InsecureSkipVerify)
	if err != nil {
		logger.Errorf("error from service passport request get_org_members: %v %v %v", err, requestUrl, string(jsonStr))
		return nil, errors.ParamsError
	}

	return respByte, nil
}

func (p *repository) InsertMember(userInfo map[string]interface{}, req *dto.ExternalInsertMember) ([]byte, error) {
	requestUrl := strings.TrimSuffix(config.Get().Passport.PassportBackendUrl, "/") + "/external/insert_member"
	currentTime := cast.ToString(time.Now().Unix())
	appid := config.Get().Passport.Appid
	appSecret := config.Get().Passport.AppSecret
	uuid := uuid.New().String()

	sign := helper.GeneratorPassportSign(appid, appSecret, uuid, currentTime)
	requestData := map[string]interface{}{
		"sign":      sign,
		"appid":     cast.ToInt(appid),
		"time":      cast.ToInt(currentTime),
		"uuid":      uuid,
		"user_info": userInfo,
		"callback":  config.Get().Passport.Callback,
		"phone":     req.Phone,
		"email":     req.Email,
		"products":  req.Products,
	}

	jsonStr, _ := json.Marshal(requestData)

	respByte, err := helper.CurlRequest(requestUrl, "POST", jsonStr, map[string]string{}, config.Get().Settings.InsecureSkipVerify)
	if err != nil {
		logger.Errorf("error from service passport request insert_member: %v %v %v", err, requestUrl, string(jsonStr))
		return nil, errors.ParamsError
	}

	return respByte, nil
}

func (p *repository) RemoveOrgUser(userInfo map[string]interface{}, req *dto.ExternalRemoveOrgUser) ([]byte, error) {
	requestUrl := strings.TrimSuffix(config.Get().Passport.PassportBackendUrl, "/") + "/external/remove_org_user"
	currentTime := cast.ToString(time.Now().Unix())
	appid := config.Get().Passport.Appid
	appSecret := config.Get().Passport.AppSecret
	uuid := uuid.New().String()

	sign := helper.GeneratorPassportSign(appid, appSecret, uuid, currentTime)
	requestData := map[string]interface{}{
		"sign":      sign,
		"appid":     cast.ToInt(appid),
		"time":      cast.ToInt(currentTime),
		"uuid":      uuid,
		"user_info": userInfo,
		"callback":  config.Get().Passport.Callback,
		"user_id":   req.UserId,
	}

	jsonStr, _ := json.Marshal(requestData)

	respByte, err := helper.CurlRequest(requestUrl, "POST", jsonStr, map[string]string{}, config.Get().Settings.InsecureSkipVerify)
	if err != nil {
		logger.Errorf("error from service passport request remove_org_user: %v %v %v", err, requestUrl, string(jsonStr))
		return nil, errors.ParamsError
	}

	return respByte, nil
}

func (p *repository) GetProductRoles(userInfo map[string]interface{}, req *dto.ExternalGetProductRoles) ([]byte, error) {
	requestUrl := strings.TrimSuffix(config.Get().Passport.PassportBackendUrl, "/") + "/external/get_product_roles"
	currentTime := cast.ToString(time.Now().Unix())
	appid := config.Get().Passport.Appid
	appSecret := config.Get().Passport.AppSecret
	uuid := uuid.New().String()

	sign := helper.GeneratorPassportSign(appid, appSecret, uuid, currentTime)
	requestData := map[string]interface{}{
		"sign":      sign,
		"appid":     cast.ToInt(appid),
		"time":      cast.ToInt(currentTime),
		"uuid":      uuid,
		"user_info": userInfo,
		"callback":  config.Get().Passport.Callback,
	}

	jsonStr, _ := json.Marshal(requestData)

	respByte, err := helper.CurlRequest(requestUrl, "POST", jsonStr, map[string]string{}, config.Get().Settings.InsecureSkipVerify)
	if err != nil {
		logger.Errorf("error from service passport request get_product_roles: %v %v %v", err, requestUrl, string(jsonStr))
		return nil, errors.ParamsError
	}

	return respByte, nil
}

func (p *repository) GetUserOrgApp(userInfo map[string]interface{}, req *dto.ExternalGetUserOrgApp) ([]byte, error) {
	requestUrl := strings.TrimSuffix(config.Get().Passport.PassportBackendUrl, "/") + "/external/get_user_org_app"
	currentTime := cast.ToString(time.Now().Unix())
	appid := config.Get().Passport.Appid
	appSecret := config.Get().Passport.AppSecret
	uuid := uuid.New().String()

	sign := helper.GeneratorPassportSign(appid, appSecret, uuid, currentTime)
	requestData := map[string]interface{}{
		"sign":      sign,
		"appid":     cast.ToInt(appid),
		"time":      cast.ToInt(currentTime),
		"uuid":      uuid,
		"user_info": userInfo,
		"callback":  config.Get().Passport.Callback,
		"user_id":   req.UserId,
	}

	jsonStr, _ := json.Marshal(requestData)

	respByte, err := helper.CurlRequest(requestUrl, "POST", jsonStr, map[string]string{}, config.Get().Settings.InsecureSkipVerify)
	if err != nil {
		logger.Errorf("error from service passport request get_user_org_app: %v %v %v", err, requestUrl, string(jsonStr))
		return nil, errors.ParamsError
	}

	return respByte, nil
}

func (p *repository) UpdateUserOrgAppRole(userInfo map[string]interface{}, req *dto.ExternalUpdateUserOrgAppRole) ([]byte, error) {
	requestUrl := strings.TrimSuffix(config.Get().Passport.PassportBackendUrl, "/") + "/external/update_user_org_app_role"
	currentTime := cast.ToString(time.Now().Unix())
	appid := config.Get().Passport.Appid
	appSecret := config.Get().Passport.AppSecret
	uuid := uuid.New().String()

	sign := helper.GeneratorPassportSign(appid, appSecret, uuid, currentTime)
	requestData := map[string]interface{}{
		"sign":      sign,
		"appid":     cast.ToInt(appid),
		"time":      cast.ToInt(currentTime),
		"uuid":      uuid,
		"user_info": userInfo,
		"callback":  config.Get().Passport.Callback,
		"user_id":   req.UserId,
		"products":  req.Products,
	}

	jsonStr, _ := json.Marshal(requestData)

	respByte, err := helper.CurlRequest(requestUrl, "POST", jsonStr, map[string]string{}, config.Get().Settings.InsecureSkipVerify)
	if err != nil {
		logger.Errorf("error from service passport request update_user_org_app_role: %v %v %v", err, requestUrl, string(jsonStr))
		return nil, errors.ParamsError
	}

	return respByte, nil
}

func (p *repository) InvitationList(userInfo map[string]interface{}, req *dto.ExternalInvitationList) ([]byte, error) {
	requestUrl := strings.TrimSuffix(config.Get().Passport.PassportBackendUrl, "/") + "/external/invitation_list"
	currentTime := cast.ToString(time.Now().Unix())
	appid := config.Get().Passport.Appid
	appSecret := config.Get().Passport.AppSecret
	uuid := uuid.New().String()

	sign := helper.GeneratorPassportSign(appid, appSecret, uuid, currentTime)
	requestData := map[string]interface{}{
		"sign":      sign,
		"appid":     cast.ToInt(appid),
		"time":      cast.ToInt(currentTime),
		"uuid":      uuid,
		"user_info": userInfo,
		"callback":  config.Get().Passport.Callback,
		"page":      req.Page,
		"pagesize":  req.Limit,
	}

	jsonStr, _ := json.Marshal(requestData)

	respByte, err := helper.CurlRequest(requestUrl, "POST", jsonStr, map[string]string{}, config.Get().Settings.InsecureSkipVerify)
	if err != nil {
		logger.Errorf("error from service passport request invitation_list: %v %v %v", err, requestUrl, string(jsonStr))
		return nil, errors.ParamsError
	}

	return respByte, nil
}

func (p *repository) UpdateInvitationStatus(userInfo map[string]interface{}, req *dto.ExternalUpdateInvitationStatus) ([]byte, error) {
	requestUrl := strings.TrimSuffix(config.Get().Passport.PassportBackendUrl, "/") + "/external/update_invitation_status"
	currentTime := cast.ToString(time.Now().Unix())
	appid := config.Get().Passport.Appid
	appSecret := config.Get().Passport.AppSecret
	uuid := uuid.New().String()

	sign := helper.GeneratorPassportSign(appid, appSecret, uuid, currentTime)
	requestData := map[string]interface{}{
		"sign":      sign,
		"appid":     cast.ToInt(appid),
		"time":      cast.ToInt(currentTime),
		"uuid":      uuid,
		"user_info": userInfo,
		"callback":  config.Get().Passport.Callback,
		"status":    req.Status,
		"id":        req.Id,
	}

	jsonStr, _ := json.Marshal(requestData)

	respByte, err := helper.CurlRequest(requestUrl, "POST", jsonStr, map[string]string{}, config.Get().Settings.InsecureSkipVerify)
	if err != nil {
		logger.Errorf("error from service passport request update_invitation_status: %v %v %v", err, requestUrl, string(jsonStr))
		return nil, errors.ParamsError
	}

	return respByte, nil
}

func (p *repository) GetMessageList(userInfo map[string]interface{}, req *dto.ExternalGetMessageList) ([]byte, error) {
	requestUrl := strings.TrimSuffix(config.Get().Passport.PassportBackendUrl, "/") + "/external/get_message_list"
	currentTime := cast.ToString(time.Now().Unix())
	appid := config.Get().Passport.Appid
	appSecret := config.Get().Passport.AppSecret
	uuid := uuid.New().String()

	sign := helper.GeneratorPassportSign(appid, appSecret, uuid, currentTime)
	requestData := map[string]interface{}{
		"sign":      sign,
		"appid":     cast.ToInt(appid),
		"time":      cast.ToInt(currentTime),
		"uuid":      uuid,
		"user_info": userInfo,
		"callback":  config.Get().Passport.Callback,
		"list_type": req.ListType,
		"msg_type":  req.MsgType,
		"page":      req.Page,
		"pagesize":  req.Limit,
	}

	jsonStr, _ := json.Marshal(requestData)

	respByte, err := helper.CurlRequest(requestUrl, "POST", jsonStr, map[string]string{}, config.Get().Settings.InsecureSkipVerify)
	if err != nil {
		logger.Errorf("error from service passport request get_message_list: %v %v %v", err, requestUrl, string(jsonStr))
		return nil, errors.ParamsError
	}

	return respByte, nil
}

func (p *repository) GetUnreadNum(userInfo map[string]interface{}, req *dto.ExternalGetUnreadNum) ([]byte, error) {
	requestUrl := strings.TrimSuffix(config.Get().Passport.PassportBackendUrl, "/") + "/external/get_unread_num"
	currentTime := cast.ToString(time.Now().Unix())
	appid := config.Get().Passport.Appid
	appSecret := config.Get().Passport.AppSecret
	uuid := uuid.New().String()

	sign := helper.GeneratorPassportSign(appid, appSecret, uuid, currentTime)
	requestData := map[string]interface{}{
		"sign":      sign,
		"appid":     cast.ToInt(appid),
		"time":      cast.ToInt(currentTime),
		"uuid":      uuid,
		"user_info": userInfo,
		"callback":  config.Get().Passport.Callback,
	}

	jsonStr, _ := json.Marshal(requestData)

	respByte, err := helper.CurlRequest(requestUrl, "POST", jsonStr, map[string]string{}, config.Get().Settings.InsecureSkipVerify)
	if err != nil {
		logger.Errorf("error from service passport request get_unread_num: %v %v %v", err, requestUrl, string(jsonStr))
		return nil, errors.ParamsError
	}

	return respByte, nil
}

func (p *repository) MarkRead(userInfo map[string]interface{}, req *dto.ExternalMarkRead) ([]byte, error) {
	requestUrl := strings.TrimSuffix(config.Get().Passport.PassportBackendUrl, "/") + "/external/mark_read"
	currentTime := cast.ToString(time.Now().Unix())
	appid := config.Get().Passport.Appid
	appSecret := config.Get().Passport.AppSecret
	uuid := uuid.New().String()

	sign := helper.GeneratorPassportSign(appid, appSecret, uuid, currentTime)
	requestData := map[string]interface{}{
		"sign":      sign,
		"appid":     cast.ToInt(appid),
		"time":      cast.ToInt(currentTime),
		"uuid":      uuid,
		"user_info": userInfo,
		"callback":  config.Get().Passport.Callback,
		"ids":       req.Ids,
	}

	jsonStr, _ := json.Marshal(requestData)

	respByte, err := helper.CurlRequest(requestUrl, "POST", jsonStr, map[string]string{}, config.Get().Settings.InsecureSkipVerify)
	if err != nil {
		logger.Errorf("error from service passport request mark_read: %v %v %v", err, requestUrl, string(jsonStr))
		return nil, errors.ParamsError
	}

	return respByte, nil
}

func (p *repository) MessageDetail(userInfo map[string]interface{}, req *dto.ExternalMessageDetail) ([]byte, error) {
	requestUrl := strings.TrimSuffix(config.Get().Passport.PassportBackendUrl, "/") + "/external/message_detail"
	currentTime := cast.ToString(time.Now().Unix())
	appid := config.Get().Passport.Appid
	appSecret := config.Get().Passport.AppSecret
	uuid := uuid.New().String()

	sign := helper.GeneratorPassportSign(appid, appSecret, uuid, currentTime)
	requestData := map[string]interface{}{
		"sign":      sign,
		"appid":     cast.ToInt(appid),
		"time":      cast.ToInt(currentTime),
		"uuid":      uuid,
		"user_info": userInfo,
		"callback":  config.Get().Passport.Callback,
		"id":        req.Id,
	}

	jsonStr, _ := json.Marshal(requestData)

	respByte, err := helper.CurlRequest(requestUrl, "POST", jsonStr, map[string]string{}, config.Get().Settings.InsecureSkipVerify)
	if err != nil {
		logger.Errorf("error from service passport request message_detail: %v %v %v", err, requestUrl, string(jsonStr))
		return nil, errors.ParamsError
	}

	return respByte, nil
}
