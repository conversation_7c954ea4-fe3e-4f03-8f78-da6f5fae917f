package office_setting

import (
	"context"
	"errors"
	"fmt"
	"time"

	"sase-client-info/pkg/logger"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

const (
	// 集合名称
	CollectionName = "office_setting"

	// 平台类型
	PlatformDingTalk = 1 // 钉钉
	PlatformWeCom    = 2 // 企业微信
	PlatformFeishu   = 3 // 飞书

	// 类型
	TypeOffice    = 1 // 协同办公平台
	TypePrivateID = 2 // 私有源身份

	// 是否开启自动同步
	AutoSyncEnable  = 1 // 开启
	AutoSyncDisable = 2 // 不开启

	// 同步模式
	SyncModelOrgAndUser = 1 // 同步组织架构和人员信息
)

// OfficeSettingModel 办公设置模型
type OfficeSettingModel struct {
	ID         primitive.ObjectID `bson:"_id,omitempty" json:"id"`
	OrgName    string             `bson:"org_name" json:"org_name"`       // 组织名称
	Platform   int                `bson:"platform" json:"platform"`       // 平台: 1-钉钉 2-企业微信 3-飞书
	Type       int                `bson:"type" json:"type"`               // 类型: 1-协同办公平台 2-私有源身份
	Name       string             `bson:"name" json:"name"`               // 组织名称
	CorpID     string             `bson:"corp_id" json:"corp_id"`         // 企业ID
	AppKey     string             `bson:"app_key" json:"app_key"`         // 应用Key
	Secret     string             `bson:"secret" json:"secret"`           // 密钥
	AutoSync   int                `bson:"auto_sync" json:"auto_sync"`     // 是否开启自动同步: 1-开启 2-不开启
	SyncCycle  string             `bson:"sync_cycle" json:"sync_cycle"`   // 自动同步周期
	SyncModel  int                `bson:"sync_model" json:"sync_model"`   // 同步模式: 1-同步组织架构和人员信息
	IsDeleted  int                `bson:"is_deleted" json:"is_deleted"`   // 是否删除: 0-未删除 1-已删除
	CreateTime int64              `bson:"create_time" json:"create_time"` // 创建时间
	UpdateTime int64              `bson:"update_time" json:"update_time"` // 更新时间
}

// Repository 定义OfficeSettingRepository接口
type Repository interface {
	Create(ctx context.Context, setting *OfficeSettingModel) error
	Update(ctx context.Context, id string, setting *OfficeSettingModel) error
	Delete(ctx context.Context, id string) error
	FindByID(ctx context.Context, id string) (*OfficeSettingModel, error)
	FindAll(ctx context.Context, page, pageSize int, filters map[string]interface{}) ([]*OfficeSettingModel, int64, error)
}

// MongoRepository MongoDB实现
type repository struct {
	collection *mongo.Collection
}

// NewRepository 创建新的仓库实例
func NewRepository() Repository {
	logger.Info("使用MongoDB作为办公设置仓库")
	// collection := resource.Database.Collection(CollectionName)
	return &repository{
		// collection: collection,
	}
}

// Create 创建办公设置
func (r *repository) Create(ctx context.Context, setting *OfficeSettingModel) error {
	if setting == nil {
		return errors.New("办公设置不能为空")
	}

	// 生成新的ID
	setting.ID = primitive.NewObjectID()

	// 设置时间戳
	now := time.Now().Unix()
	setting.CreateTime = now
	setting.UpdateTime = now

	// 默认为未删除状态
	setting.IsDeleted = 0

	_, err := r.collection.InsertOne(ctx, setting)
	if err != nil {
		logger.WithError(err, logger.ERROR, "创建办公设置失败")
		return fmt.Errorf("创建办公设置失败: %w", err)
	}

	return nil
}

// Update 更新办公设置
func (r *repository) Update(ctx context.Context, id string, setting *OfficeSettingModel) error {
	if setting == nil {
		return errors.New("办公设置不能为空")
	}

	// 解析ID
	objID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return fmt.Errorf("无效的ID格式: %w", err)
	}

	// 设置更新时间
	setting.UpdateTime = time.Now().Unix()

	// 构建更新文档
	update := bson.M{
		"$set": bson.M{
			"org_name":    setting.OrgName,
			"platform":    setting.Platform,
			"type":        setting.Type,
			"name":        setting.Name,
			"corp_id":     setting.CorpID,
			"app_key":     setting.AppKey,
			"secret":      setting.Secret,
			"auto_sync":   setting.AutoSync,
			"sync_cycle":  setting.SyncCycle,
			"sync_model":  setting.SyncModel,
			"is_deleted":  setting.IsDeleted,
			"update_time": setting.UpdateTime,
		},
	}

	filter := bson.M{"_id": objID}
	result, err := r.collection.UpdateOne(ctx, filter, update)
	if err != nil {
		logger.WithError(err, logger.ERROR, "更新办公设置失败")
		return fmt.Errorf("更新办公设置失败: %w", err)
	}

	if result.MatchedCount == 0 {
		return errors.New("未找到要更新的办公设置")
	}

	return nil
}

// Delete 删除办公设置 (软删除)
func (r *repository) Delete(ctx context.Context, id string) error {

	// 解析ID
	objID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return fmt.Errorf("无效的ID格式: %w", err)
	}

	// 软删除 - 更新is_deleted字段为1
	filter := bson.M{"_id": objID, "is_deleted": 0}
	update := bson.M{
		"$set": bson.M{
			"is_deleted":  1,
			"update_time": time.Now().Unix(),
		},
	}

	result, err := r.collection.UpdateOne(ctx, filter, update)
	if err != nil {
		logger.WithError(err, logger.ERROR, "删除办公设置失败")
		return fmt.Errorf("删除办公设置失败: %w", err)
	}

	if result.MatchedCount == 0 {
		return errors.New("未找到要删除的办公设置或已被删除")
	}

	return nil
}

// FindByID 通过ID查找办公设置
func (r *repository) FindByID(ctx context.Context, id string) (*OfficeSettingModel, error) {

	// 解析ID
	objID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, fmt.Errorf("无效的ID格式: %w", err)
	}

	// 查询未删除的记录
	filter := bson.M{"_id": objID, "is_deleted": 0}
	var setting OfficeSettingModel

	err = r.collection.FindOne(ctx, filter).Decode(&setting)
	if err != nil {
		logger.WithError(err, logger.ERROR, "查找办公设置失败")
		return nil, fmt.Errorf("查找办公设置失败: %w", err)
	}

	return &setting, nil
}

// FindAll 分页查询办公设置
func (r *repository) FindAll(ctx context.Context, page, pageSize int, filters map[string]interface{}) ([]*OfficeSettingModel, int64, error) {

	// 构建查询条件
	filter := bson.M{"is_deleted": 0} // 只查询未删除的记录
	for k, v := range filters {
		filter[k] = v
	}

	// 计算总数
	total, err := r.collection.CountDocuments(ctx, filter)
	if err != nil {
		logger.WithError(err, logger.ERROR, "统计办公设置数量失败")
		return nil, 0, fmt.Errorf("统计办公设置数量失败: %w", err)
	}

	// 如果没有数据，直接返回空结果
	if total == 0 {
		return []*OfficeSettingModel{}, 0, nil
	}

	// 设置分页参数
	findOptions := options.Find()
	if page > 0 && pageSize > 0 {
		findOptions.SetSkip(int64((page - 1) * pageSize))
		findOptions.SetLimit(int64(pageSize))
	}
	// 按创建时间倒序排序
	findOptions.SetSort(bson.M{"create_time": -1})

	// 执行查询
	cursor, err := r.collection.Find(ctx, filter, findOptions)
	if err != nil {
		logger.WithError(err, logger.ERROR, "查询办公设置失败")
		return nil, 0, fmt.Errorf("查询办公设置失败: %w", err)
	}
	defer cursor.Close(ctx)

	// 解析结果
	var settings []*OfficeSettingModel
	for cursor.Next(ctx) {
		var setting OfficeSettingModel
		if err := cursor.Decode(&setting); err != nil {
			logger.WithError(err, logger.ERROR, "解析办公设置数据失败")
			return nil, 0, fmt.Errorf("解析办公设置数据失败: %w", err)
		}
		settings = append(settings, &setting)
	}

	if err := cursor.Err(); err != nil {
		logger.WithError(err, logger.ERROR, "遍历办公设置数据失败")
		return nil, 0, fmt.Errorf("遍历办公设置数据失败: %w", err)
	}

	return settings, total, nil
}
