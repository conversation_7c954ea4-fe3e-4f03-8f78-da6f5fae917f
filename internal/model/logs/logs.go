package logs

import (
	"context"
	"encoding/json"
	"fmt"
	"sase-client-info/internal/common/consts"
	"sase-client-info/internal/common/errors"
	"sase-client-info/internal/config"
	"sase-client-info/pkg/logger"
	"sase-client-info/pkg/utils/helper"

	"github.com/dromara/carbon/v2"
	"github.com/olivere/elastic/v7"
	"github.com/xuri/excelize/v2"

	"sase-client-info/internal/dto"
	"sase-client-info/internal/resource"
	"strings"
	"time"

	"github.com/spf13/cast"
)

type EsAuditLog struct {
	UserName string `json:"user_name"`
	UId      string `json:"uid"`
	Path     string `json:"path"`
	//Request    interface{} `json:"request.body"`
	Payload    interface{} `json:"payload"`
	LogType    string      `json:"log_type"`
	Time       int64       `json:"time"`
	Action     string      `json:"action"`
	ActionType string      `json:"action_type"`
	Operator   string      `json:"operator"`
	Event      string      `json:"event"`
	Ip         string      `json:"ip"`
	Id         string      `json:"id"`
}

type ESLog struct {
	RmMessage *EsAuditLog `json:"rm_message"`
}

type Repository interface {
	ListAuditLog(ctx context.Context, req *dto.AuditLogListRequest, orgName string) (interface{}, error)
	SetValueToCsv2(data interface{}, language string) (*excelize.File, string, error)
}

type repository struct {
}

func NewRepository() *repository {
	return &repository{}
}
func (r *repository) ListAuditLog(ctx context.Context, req *dto.AuditLogListRequest, orgName string) (interface{}, error) {

	skip := req.Limit
	if req.IsExport == 1 {
		skip = 0
		req.Limit = config.Get().Server.ExportLimit
	} else {
		skip = (req.Page - 1) * req.Limit
	}
	var startTime, endTime int64
	// todo 时间处理
	if len(req.CreateTime) == 2 {
		startTime = req.CreateTime[0]
		endTime = req.CreateTime[1]
	}

	query := elastic.NewBoolQuery()

	listQuery := make([]elastic.Query, 0)

	listQuery = append(listQuery, elastic.NewTermQuery("rm_message.org_name.keyword", orgName))
	listQuery = append(listQuery, elastic.NewTermQuery("rm_message.view_type", 1)) //审计日志
	if startTime != 0 && endTime != 0 {
		listQuery = append(listQuery, elastic.NewRangeQuery("rm_message.time").Gt(startTime).Lte(endTime))
	} else {
		listQuery = append(listQuery, elastic.NewRangeQuery("rm_message.time").Gte(time.Now().AddDate(-3, 0, 0).Unix()))
	}

	if req.Ip != "" {
		listQuery = append(listQuery, elastic.NewTermQuery("rm_message.ip.keyword", req.Ip))
	}
	if req.UserName != "" {
		if strings.Contains(req.UserName, "*") {
			req.UserName = strings.ReplaceAll(req.UserName, "*", "?")
		}
		listQuery = append(listQuery, elastic.NewWildcardQuery("rm_message.user_name.keyword", fmt.Sprintf("*%s*", req.UserName)))
	}
	if req.LogType != "" {
		listQuery = append(listQuery, elastic.NewTermQuery("rm_message.log_type.keyword", req.LogType))
	}

	if req.Action != "" {
		actions := strings.Split(req.Action, ",") // 假设 req.Action 是用逗号分隔的字符串
		var data = make([]interface{}, 0, len(actions))
		for _, action := range actions {
			data = append(data, action)
		}
		listQuery = append(listQuery, elastic.NewTermsQuery("rm_message.event.keyword", data...))
	}
	fmt.Printf("%+v\n", listQuery)
	query.Filter(listQuery...)

	total, err := resource.Es.Client.Count(config.Get().ElasticSearchExt.ProxyIndexName).Query(query).Do(ctx)

	cursor, err := resource.Es.Client.Search(config.Get().ElasticSearchExt.ProxyIndexName).
		Query(query).
		FetchSourceContext(elastic.NewFetchSourceContext(true).Include("rm_message")). // 指定返回字段
		Sort("@timestamp", false).
		From(int(skip)).
		Size(int(req.Limit)).
		Do(ctx)
	if err != nil {
		return nil, err
	}
	var esLogs = make([]*dto.EsAuditLog, 0)
	for _, hit := range cursor.Hits.Hits {
		eslog := dto.ESLog{}
		_ = json.Unmarshal(hit.Source, &eslog)
		eslog.RmMessage.Id = hit.Id

		var dataMap = make(map[string]interface{})

		err = json.Unmarshal([]byte(cast.ToString(eslog.RmMessage.Payload)), &dataMap)
		if err == nil {
			eslog.RmMessage.Payload = dataMap
		}

		if _, ok := eslog.RmMessage.Payload.(map[string]interface{}); !ok {
			eslog.RmMessage.Payload = map[string]interface{}{}
		}

		esLogs = append(esLogs, eslog.RmMessage)

	}

	return &dto.AuditLogListResp{
		Results: esLogs,
		Total:   total,
		//Pages:   pages,
	}, nil
}

func (r *repository) SetValueToCsv2(data interface{}, language string) (*excelize.File, string, error) {
	var err error
	var fileName string
	var f *excelize.File

	auditData, _ := data.(*dto.AuditLogListResp)
	if language == consts.LanguageEn {
		fileName = "Audit-ExportResults" + carbon.Now().Format("YmdHis") + ".xlsx"
		f, err = excelize.OpenFile(helper.GetExcelTplPath("audit_log_en.xlsx"))
	} else {

		fileName = "戎码翼龙审计日志列表" + carbon.Now().Format("YmdHis") + ".xlsx"

		f, err = excelize.OpenFile(helper.GetExcelTplPath("audit_log.xlsx"))
	}

	if err != nil {
		logger.Errorf("error from service alarms SetValueToCsv T: %v", err)
		return nil, fileName, errors.SelectError
	}

	dataMap := consts.AuditMap[language]

	index := 2
	if auditData != nil {
		if len(auditData.Results) > 0 {
			for _, v := range auditData.Results {
				excelIndex := cast.ToString(index)
				if _, ok := dataMap[v.Event]; !ok {
					continue
				}
				_ = f.SetCellValue("Sheet1", "A"+excelIndex, v.UserName)
				_ = f.SetCellValue("Sheet1", "B"+excelIndex, v.Ip)
				_ = f.SetCellValue("Sheet1", "C"+excelIndex, dataMap[v.Event].Action)
				_ = f.SetCellValue("Sheet1", "D"+excelIndex, dataMap[v.Event].LogType)
				if v.Payload != nil {
					_ = f.SetCellValue("Sheet1", "E"+excelIndex, dataMap[v.Event].Description(v.Payload.(map[string]interface{})))
				} else {
					_ = f.SetCellValue("Sheet1", "E"+excelIndex, "")
				}
				_ = f.SetCellValue("Sheet1", "F"+excelIndex, time.Unix(v.Time, 0).Format(time.DateTime))
				index += 1
			}
		}
	}

	return f, fileName, nil
}
