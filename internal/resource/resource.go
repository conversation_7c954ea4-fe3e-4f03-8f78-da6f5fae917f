package resource

import (
	"log"
	"sase-client-info/internal/config"
	"sase-client-info/pkg/auth/jwt"
	"sase-client-info/pkg/client"
	"sase-client-info/pkg/logger"

	"rm.git/client_api/rm_common_libs.git/v2/common/clients"
	rmCommonInterfaces "rm.git/client_api/rm_common_libs.git/v2/interfaces"
)

var (
	// Database    *mongo.Database
	Redisclient rmCommonInterfaces.RedisClient

	Jwt *jwt.JWTAuth

	LogRecorder *client.ActLogger
	Es          *clients.ElasticSearch
)

func Init() {
	var err error

	Redisclient, err = clients.NewRedis(config.Get().Redis)
	if err != nil {
		logger.Fatalf("初始化Redis连接失败: %v", err)
	}

	Jwt, err = jwt.Init(config.Get().Jwt.Prefix, config.Get().Jwt.SignKey, config.Get().Jwt.ExpiredTime, Redisclient)
	if err != nil {
		logger.Fatalf("jwt init error: %v", err)
	}
	logger.Infof("初始化 jwt 成功")

	LogRecorder = client.NewActLogger(client.WithIndexName(config.Get().ActLogger.ActlogIndexName), client.WithDebug(config.Get().ActLogger.IsDebug), client.WithLogType(1))

	Es, err = clients.NewElasticSearch(config.Get().Elasticsearch)
	if err != nil {
		log.Fatalln(err)
	}

}

func Close() {
	Redisclient.Close()
}
