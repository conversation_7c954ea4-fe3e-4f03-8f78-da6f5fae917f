package config

import (
	"flag"
	"fmt"

	"rm.git/client_api/rm_common_libs.git/v2/common/configs"

	"github.com/spf13/viper"
)

// Configuration 全局配置结构体
type Configuration struct {
	Server           ServerConfig                `mapstructure:"server"`
	Redis            configs.RedisConfig         `mapstructure:"redis"`
	Log              LogConfig                   `mapstructure:"log"`
	Passport         Passport                    `mapstructure:"passport"`
	Jwt              Jwt                         `mapstructure:"jwt"`
	Settings         Settings                    `mapstructure:"settings"`
	DevTest          DevTest                     `mapstructure:"dev_test"`
	Email            EmailConfig                 `mapstructure:"email"`
	VerifyCode       VerifyCode                  `mapstructure:"verify_code"`
	Elasticsearch    configs.ElasticSearchConfig `mapstructure:"elasticsearch_default"`
	ActLogger        configs.ActLoggerConfig     `mapstructure:"actlogger"`
	ElasticSearchExt ElasticSearchExt            `mapstructure:"elasticsearch_ext"`
	PassportUriMap   map[string]string           `mapstructure:"passport_uri_map"`
	TsMlmConfig      TsMlmConfig                 `mapstructure:"ts_mlm_config"`
}

type ElasticSearchExt struct {
	ProxyIndexName string `mapstructure:"proxy_index_name"`
}

type Settings struct {
	InsecureSkipVerify bool `mapstructure:"insecure_skip_verify"`
}

// Passport 通行证配置
type Passport struct {
	Callback              string `mapstructure:"callback"`
	Appid                 string `mapstructure:"appid"`
	AppSecret             string `mapstructure:"app_secret"`
	PassportWebUrl        string `mapstructure:"passport_web_url"`
	PassportBackendUrl    string `mapstructure:"passport_backend_url"`
	ConsoleWebCallbackUrl string `mapstructure:"console_web_callback_url"`
}

// Jwt JWT配置
type Jwt struct {
	ExpiredTime string `mapstructure:"expired_time"`
	Prefix      string `mapstructure:"prefix"`
	SignKey     string `mapstructure:"sign_key"`
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Port                     int      `mapstructure:"port"`
	Mode                     string   `mapstructure:"mode"`
	AccessControlAllowOrigin []string `mapstructure:"access_control_allow_origin"`
	SaseContext              string   `mapstructure:"sase_context"`
	SaseCloudApi             string   `mapstructure:"sase_cloud_api"`
	LogoutJumpUrl            string   `mapstructure:"logout_jump_url"`
	Name                     string   `mapstructure:"name"`
	ExportLimit              int64    `mapstructure:"export_limit"`
	CompanyCode              string   `mapstructure:"company_code"`
	CompanyBrandName         string   `mapstructure:"company_brand_name"`
}

// LogConfig 日志配置
type LogConfig struct {
	Level string `mapstructure:"level"`
	Path  string `mapstructure:"path"`
}

// DevTest 开发测试配置
type DevTest struct {
	Switch        bool   `mapstructure:"switch"`
	UserId        string `mapstructure:"user_id"`
	UserName      string `mapstructure:"user_name"`
	EdrDcsApi     string `mapstructure:"edr_dcs_api"`
	EdrProductId  string `mapstructure:"edr_product_id"`
	SaseDcsApi    string `mapstructure:"sase_dcs_api"`
	SaseProductId string `mapstructure:"sase_product_id"`
	Ciphertext    string `mapstructure:"ciphertext"`
}

// EmailConfig 邮箱配置
type EmailConfig struct {
	Port      int    `mapstructure:"port"`
	Host      string `mapstructure:"host"`
	User      string `mapstructure:"user"`
	Password  string `mapstructure:"password"`
	AliasName string `mapstructure:"alias_name"`
}

type VerifyCode struct {
	DefaultInterval               string `mapstructure:"default_interval"`
	IsAllowTestPhone              bool   `mapstructure:"is_allow_test_phone"`
	SmsSecretId                   string `mapstructure:"sms_secret_id"`
	SmsSecretKey                  string `mapstructure:"sms_secret_key"`
	SmsSdkAppid                   string `mapstructure:"sms_sdk_appid"`
	SmsSignName                   string `mapstructure:"sms_sign_name"`
	SmsTplId                      string `mapstructure:"sms_tpl_id"`
	VerificationCodeInterval      int    `mapstructure:"verification_code_interval"`
	VerificationCodeValidTime     int    `mapstructure:"verification_code_valid_time"`
	VerificationCodeTimesDay      int    `mapstructure:"verification_code_times_day"`
	VerificationCodeTimesDayForIp int    `mapstructure:"verification_code_times_day_for_ip"`
}

type TsMlmConfig struct {
	Token              string `mapstructure:"token"`
	User               string `mapstructure:"user"`
	SmsAddress         string `mapstructure:"sms_address"`
	EmailToken         string `mapstructure:"email_token"`
	EmailAddress       string `mapstructure:"email_address"`
	EmailFrom          string `mapstructure:"email_from"`
	InsecureSkipVerify bool   `mapstructure:"insecure_skip_verify"`
}

var config Configuration

// Init 初始化配置
func Init() error {
	// 设置配置文件路径
	configPath := ""
	flag.StringVar(&configPath, "c", "./configs", "config path")
	flag.Parse()
	viper.AddConfigPath(configPath)
	viper.SetConfigName("config")
	viper.SetConfigType("toml")

	// 读取环境变量
	viper.AutomaticEnv()

	// 读取配置文件
	if err := viper.ReadInConfig(); err != nil {
		return fmt.Errorf("读取配置文件失败: %w", err)
	}

	// 解析配置到结构体
	if err := viper.Unmarshal(&config); err != nil {
		return fmt.Errorf("解析配置失败: %w", err)
	}

	return nil
}

// Get 获取配置
func Get() Configuration {
	return config
}
