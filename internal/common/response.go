package common

import (
	"encoding/json"
	"fmt"
	"net/http"

	"sase-client-info/internal/common/errors"

	"github.com/gin-gonic/gin"
)

type Response struct {
	Error   int         `json:"error"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}

const (
	LanguageZh = "zh"
	LanguageEn = "en"
)

func ResponseResult(ctx *gin.Context, v interface{}, data ...interface{}) {
	response := newResponse(v, ctx.GetString("language"))

	if len(data) > 0 {
		response.Data = data[0]

	}
	ctx.Header("Content-Type", "application/json")
	ctx.IndentedJSON(http.StatusOK, response)
}

func newResponse(v interface{}, language string) *Response {
	var resp *Response
	var message string

	switch v := v.(type) {
	case errors.ErrorCode:
		if language == LanguageEn {
			message = v.MessageEn
		} else {
			message = v.Message
		}
		resp = &Response{Error: v.Code, Message: message, Data: ""}
	case error:
		resp = &Response{Error: -1, Message: v.Error(), Data: ""}
	default:
		resp = &Response{Error: 0, Message: "success", Data: v}
	}

	return resp
}

func (r *Response) Encode() []byte {
	encoded, _ := json.Marshal(r)
	return encoded
}

func (r *Response) Decode(v interface{}) error {
	switch r.Data.(type) {
	case string:
		return json.Unmarshal([]byte(r.Data.(string)), v)
	case []byte:
		return json.Unmarshal(r.Data.([]byte), v)
	default:
		return fmt.Errorf("cannot decode data of type: %T", r.Data)
	}
}
