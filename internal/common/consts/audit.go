package consts

import (
	"fmt"
	"github.com/spf13/cast"
	"strings"
)

const (
	NotifyImSettingImType1 = 1 // 钉钉
	NotifyImSettingImType2 = 2 // 飞书
	NotifyImSettingImType3 = 3 // 微信
)

var AuditMap = map[string]map[string]struct {
	Description func(map[string]interface{}) string
	Action      string
	LogType     string
}{
	LanguageZh: {
		"event.user.login": {
			Description: func(m map[string]interface{}) string {
				return "登录成功"
			},
			Action:  "登录",
			LogType: "访问日志",
		},
		"event.user.logout": {
			Description: func(m map[string]interface{}) string {
				return "退出登录成功"
			},
			Action:  "退出登录",
			LogType: "访问日志",
		},
		"event.instruction_policy.add": {
			Description: func(m map[string]interface{}) string {
				return fmt.Sprintf("新建自动响应策略“%s”成功", m["name"])
			},
			Action:  "创建自动响应策略",
			LogType: "操作日志",
		},
		"event.instruction_policy.update": {
			Description: func(m map[string]interface{}) string {
				return fmt.Sprintf("编辑自动响应策略“%s”成功", m["name"])
			},
			LogType: "操作日志",
			Action:  "编辑自动响应策略",
		},
		"event.instruction_policy.delete": {
			Description: func(m map[string]interface{}) string {
				return fmt.Sprintf("删除自动响应策略“%s”成功", m["name"])
			},
			LogType: "操作日志",
			Action:  "删除自动响应策略",
		},
		"event.instruction.batch_kill_ps": {
			Description: func(m map[string]interface{}) string {
				return fmt.Sprintf("下发结束进程任务成功，响应ID：%s", m["task_id"])
			},
			LogType: "操作日志",
			Action:  "下发响应任务-结束进程",
		},
		"event.instruction.batch_quarantine_file": {
			Description: func(m map[string]interface{}) string {
				return fmt.Sprintf("下发响应任务-隔离文件成功，响应ID：%s", m["task_id"])
			},
			LogType: "操作日志",
			Action:  "下发响应任务-隔离文件",
		},
		"event.instruction.quarantine_network": {
			Description: func(m map[string]interface{}) string {
				return fmt.Sprintf("下发响应任务-隔离主机成功，响应ID：%s", m["task_id"])
			},
			LogType: "操作日志",
			Action:  "下发响应任务-隔离主机",
		},
		"event.instruction.recover_network": {
			Description: func(m map[string]interface{}) string {
				return fmt.Sprintf("下发响应任务-恢复主机连接网络成功，响应ID：%s", m["task_id"])
			},
			LogType: "操作日志",
			Action:  "下发响应任务-恢复主机连接网络",
		},
		"event.instruction.get_suspicious_file": {
			Description: func(m map[string]interface{}) string {
				return fmt.Sprintf("下发响应任务-获取可疑文件成功，响应ID：%s", m["task_id"])
			},
			LogType: "操作日志",
			Action:  "下发响应任务-获取可疑文件",
		},
		"event.instruction.list_ps": {
			Description: func(m map[string]interface{}) string {
				return fmt.Sprintf("下发响应任务-浏览进程成功，响应ID：%s", m["task_id"])
			},
			LogType: "操作日志",
			Action:  "下发响应任务-浏览进程",
		},
		"event.instruction.process_dump": {
			Description: func(m map[string]interface{}) string {
				return fmt.Sprintf("下发响应任务-下载进程Dump文件成功，响应ID：%s", m["task_id"])
			},
			LogType: "操作日志",
			Action:  "下发响应任务-下载进程Dump文件",
		},
		"event.instruction.image_analyze": {
			Description: func(m map[string]interface{}) string {
				return fmt.Sprintf("下发响应任务-模块分析成功，响应ID：%s", m["task_id"])
			},
			LogType: "操作日志",
			Action:  "下发响应任务-模块分析",
		},
		"event.instruction.process_analyze": {
			Description: func(m map[string]interface{}) string {
				return fmt.Sprintf("下发响应任务-进程分析成功，响应ID：%s", m["task_id"])
			},
			LogType: "操作日志",
			Action:  "下发响应任务-进程分析",
		},
		"event.instruction.quick_malware_scan": {
			Description: func(m map[string]interface{}) string {
				return fmt.Sprintf("下发病毒查杀任务-快速扫描成功，响应ID：%s", m["task_id"])
			},
			LogType: "操作日志",
			Action:  "下发病毒查杀任务-快速扫描",
		},
		"event.instruction.full_malware_scan": {
			Description: func(m map[string]interface{}) string {
				return fmt.Sprintf("下发病毒查杀任务-全盘扫描成功，响应ID：%s", m["task_id"])
			},
			LogType: "操作日志",
			Action:  "下发病毒查杀任务-全盘扫描",
		},
		"event.instruction.custom_malware_scan": {
			Description: func(m map[string]interface{}) string {
				return fmt.Sprintf("下发病毒查杀任务-自定义路径扫描成功，响应ID：%s", m["task_id"])
			},
			LogType: "操作日志",
			Action:  "下发病毒查杀任务-自定义路径扫描",
		},
		"event.instruction.end_scan": {
			Description: func(m map[string]interface{}) string {
				return fmt.Sprintf("终止病毒查杀任务成功，响应ID：%s", m["task_id"])
			},
			LogType: "操作日志",
			Action:  "终止病毒查杀任务",
		},
		"event.isolate_file.release": {
			Description: func(m map[string]interface{}) string {
				return fmt.Sprintf("释放隔离文件")
			},
			LogType: "操作日志",
			Action:  "释放隔离文件",
		},
		"event.isolate_file.delete": {
			Description: func(m map[string]interface{}) string {
				return fmt.Sprintf("删除隔离文件")
			},
			LogType: "操作日志",
			Action:  "删除隔离文件",
		},
		"event.ioc.add": {
			Description: func(m map[string]interface{}) string {
				if m["action"] == "Allow" {
					return fmt.Sprintf("已将Hash“%s”添加为IOC白名单", m["hash"])
				}
				return fmt.Sprintf("已将Hash“%s”添加为IOC黑名单", m["hash"])
			},
			LogType: "操作日志",
			Action:  "添加IOC",
		},
		"event.host.connected": {
			Description: func(m map[string]interface{}) string {
				return fmt.Sprintf("主机连接成功，主机名: %s", m["host_name"])
			},
			LogType: "操作日志",
			Action:  "连接到主机成功",
		},
		"event.host.disconnected": {
			Description: func(m map[string]interface{}) string {
				return fmt.Sprintf("断开与主机的连接成功，主机名: %s", m["host_name"])
			},
			LogType: "操作日志",
			Action:  "断开与主机的连接",
		},
		"event.ioc.delete": {
			Description: func(m map[string]interface{}) string {
				return fmt.Sprintf("删除IOC成功")
			},
			LogType: "操作日志",
			Action:  "删除IOC",
		},
		"event.ioc.update": {
			Description: func(m map[string]interface{}) string {
				return fmt.Sprintf("编辑IOC成功")
			},
			LogType: "操作日志",
			Action:  "编辑IOC",
		},
		"event.ioa.add": {
			Description: func(m map[string]interface{}) string {
				return fmt.Sprintf("添加IOA白名单成功，白名单名称：%s，加白IOA名称：%s", m["exclusion_name"], m["ioa_name"])
			},
			LogType: "操作日志",
			Action:  "添加IOA白名单",
		},
		"event.ioa.delete": {
			Description: func(m map[string]interface{}) string {
				return fmt.Sprintf("删除IOA白名单成功")
			},
			LogType: "操作日志",
			Action:  "删除IOA白名单",
		},
		"event.ioa.update": {
			Description: func(m map[string]interface{}) string {
				return fmt.Sprintf("编辑IOA白名单成功，白名单名称：%s，加白IOA名称：%s", m["exclusion_name"], m["ioa_name"])
			},
			LogType: "操作日志",
			Action:  "编辑IOA白名单",
		},
		"event.notify.add": {
			Description: func(m map[string]interface{}) string {
				return fmt.Sprintf("添加通知策略成功，策略名称：%s", m["name"])
			},
			LogType: "操作日志",
			Action:  "添加通知策略",
		},
		"event.notify.delete": {
			Description: func(m map[string]interface{}) string {
				return "删除通知策略成功"
			},
			LogType: "操作日志",
			Action:  "删除通知策略",
		},
		"event.notify.update": {
			Description: func(m map[string]interface{}) string {
				return "编辑通知策略成功"
			},
			LogType: "操作日志",
			Action:  "编辑通知策略",
		},
		"event.robot.add": {
			Description: func(m map[string]interface{}) string {
				robotName := "钉钉"
				if cast.ToInt(m["im_type"]) == NotifyImSettingImType2 {
					robotName = "飞书"
				}
				if cast.ToInt(m["im_type"]) == NotifyImSettingImType3 {
					robotName = "企业微信"
				}

				return fmt.Sprintf("添加%s机器人成功", robotName)
			},
			Action:  "添加IM机器人",
			LogType: "操作日志",
		},
		"event.robot.delete": {
			Description: func(m map[string]interface{}) string { return "删除机器人成功" },
			Action:      "删除IM机器人",
			LogType:     "操作日志",
		},
		"event.robot.update": {
			Description: func(m map[string]interface{}) string {
				robotName := "钉钉"
				if cast.ToInt(m["im_type"]) == NotifyImSettingImType2 {
					robotName = "飞书"
				}
				if cast.ToInt(m["im_type"]) == NotifyImSettingImType3 {
					robotName = "企业微信"
				}

				return fmt.Sprintf("编辑%s机器人成功，IM配置名称：%s", robotName, m["name"])
			},
			LogType: "操作日志",
			Action:  "编辑IM机器人",
		},
		"event.client_uninstall_password.on": {
			Description: func(m map[string]interface{}) string {
				return "开启客户端卸载密码配置成功"
			},
			LogType: "操作日志",
			Action:  "开启客户端卸载密码配置",
		},
		"event.client_uninstall_password.update": {
			Description: func(m map[string]interface{}) string {
				return "修改客户端卸载密码成功"
			},
			LogType: "操作日志",
			Action:  "修改客户端卸载密码",
		},
		"event.client_uninstall_password.off": {
			Description: func(m map[string]interface{}) string {
				return "关闭客户端卸载密码配置成功"
			},
			LogType: "操作日志",
			Action:  "关闭客户端卸载密码配置",
		},
		"event.host.importance": {
			Description: func(m map[string]interface{}) string {
				if cast.ToInt(m["importance"]) == 1 {
					return fmt.Sprintf("更改客户端ID：%s 的主机重要性为“重要主机”", m["client_id"])
				}
				return fmt.Sprintf("更改客户端ID：%s 的主机重要性为“普通主机”", m["client_id"])
			},
			LogType: "操作日志",
			Action:  "更改主机重要性",
		},
		"event.detection.add": {
			Description: func(m map[string]interface{}) string {
				return fmt.Sprintf("添加狩猎规则成功，条件名称：%s", m["name"])
			},
			LogType: "操作日志",
			Action:  "添加狩猎规则",
		},
		"event.detection.delete": {
			Description: func(m map[string]interface{}) string {
				return fmt.Sprintf("删除狩猎规则成功")
			},
			LogType: "操作日志",
			Action:  "删除狩猎规则",
		},
		"event.detection.update": {
			Description: func(m map[string]interface{}) string {
				return fmt.Sprintf("编辑狩猎规则成功，条件名称：%s", m["name"])
			},
			LogType: "操作日志",
			Action:  "编辑狩猎规则",
		},
		"event.detection.export": {
			Description: func(m map[string]interface{}) string {
				return "导出狩猎告警数据成功"
			},
			LogType: "操作日志",
			Action:  "导出狩猎告警",
		},
		"event.detection.status": {
			Description: func(m map[string]interface{}) string {
				status := "未处置"
				if cast.ToInt(m["deal_status"]) == 2 {
					status = "处置中"
				}
				if cast.ToInt(m["deal_status"]) == 3 {
					status = "已处置"
				}
				if cast.ToInt(m["deal_status"]) == 4 {
					status = "误报反馈"
				}

				return fmt.Sprintf("处置%d条风险检出成功，将风险检出的处置状态更改为“%s”", len(cast.ToStringSlice(m["ids"])), status)
			},
			LogType: "操作日志",
			Action:  "处置风险检出",
		},
		"event.incident.status": {
			Description: func(m map[string]interface{}) string {
				status := "未处置"
				if cast.ToInt(m["status"]) == 2 {
					status = "处置中"
				}
				if cast.ToInt(m["status"]) == 3 {
					status = "已处置"
				}
				if cast.ToInt(m["status"]) == 4 {
					status = "误报反馈"
				}
				totalDetection := cast.ToInt(m["total_detection"])
				totalIncident := cast.ToInt(m["total_incident"])
				incidentsName := strings.Join(cast.ToStringSlice(m["incident_names"]), ",")

				if totalDetection == 0 {
					return fmt.Sprintf("将%d条威胁事件的处置状态成功更改为：%s, 所有威胁事件名称:%s", totalIncident, status, incidentsName)
				}

				return fmt.Sprintf("将%d条威胁事件及关联的%d条风险检出的处置状态成功更改为：%s, 所有威胁事件名称:%s", totalIncident, totalDetection, status, incidentsName)

			},
			LogType: "操作日志",
			Action:  "处置威胁事件",
		},
		"event.client.download": {
			Description: func(m map[string]interface{}) string {
				return fmt.Sprintf("下载客户端成功，客户端名称：%s，客户端版本：%s", m["file_name"], m["version"])
			},
			LogType: "操作日志",
			Action:  "下载客户端",
		},
	},
	LanguageEn: {
		"event.user.login": {
			Description: func(m map[string]interface{}) string {
				return "Login success"
			},
			Action:  "Login",
			LogType: "operation log",
		},
		"event.user.logout": {
			Description: func(m map[string]interface{}) string {
				return "Logout success"
			},
			Action:  "Logout",
			LogType: "access log",
		},
		"event.instruction_policy.add": {
			Description: func(m map[string]interface{}) string {
				return fmt.Sprintf("Successfully created Auto-Response Policy: “%s”", m["name"])
			},
			Action:  "Create auto-response policy",
			LogType: "operation log",
		},
		"event.instruction_policy.update": {
			Description: func(m map[string]interface{}) string {
				return fmt.Sprintf("Successfully edited Auto-Response Policy: “%s”", m["name"])
			},
			Action:  "Edit auto-response policy",
			LogType: "operation log",
		},
		"event.instruction_policy.delete": {
			Description: func(m map[string]interface{}) string {
				return fmt.Sprintf("Successfully deleted Auto-Response Policy: “%s”", m["name"])
			},
			Action:  "Delete auto-response policy",
			LogType: "operation log",
		},
		"event.instruction.batch_kill_ps": {
			Description: func(m map[string]interface{}) string {
				return fmt.Sprintf("Successfully issued Kill the process task, Response ID:%s", m["task_id"])
			},
			LogType: "operation log",
			Action:  "Issue response task - kill the process",
		},
		"event.instruction.batch_quarantine_file": {
			Description: func(m map[string]interface{}) string {
				return fmt.Sprintf("Successfully issued File Quarantine task, Response ID: %s", m["task_id"])
			},
			LogType: "operation log",
			Action:  "Issue response task - file quarantine",
		},
		"event.instruction.quarantine_network": {
			Description: func(m map[string]interface{}) string {
				return fmt.Sprintf("Successfully issued Quarantine Host task, Response ID: %s", m["task_id"])
			},
			LogType: "operation log",
			Action:  "Issue response task - quarantine host",
		},
		"event.instruction.recover_network": {
			Description: func(m map[string]interface{}) string {
				return fmt.Sprintf("Successfully issued Lift Containment task, Response ID: %s", m["task_id"])
			},
			LogType: "operation log",
			Action:  "Issue response task - lift containment",
		},

		"event.host.connected": {
			Description: func(m map[string]interface{}) string {
				return fmt.Sprintf("Connected to the host successfully, host name: %s", m["host_name"])
			},
			LogType: "operation log",
			Action:  "Connected to the host successfully",
		},
		"event.host.disconnected": {
			Description: func(m map[string]interface{}) string {
				return fmt.Sprintf("Disconnected from the host successfully, host name:  %s", m["host_name"])
			},
			LogType: "operation log",
			Action:  "Disconnect from the host",
		},

		"event.instruction.get_suspicious_file": {
			Description: func(m map[string]interface{}) string {
				return fmt.Sprintf("Successfully issued Get suspicious files task, Response ID:%s", m["task_id"])
			},
			LogType: "operation log",
			Action:  "Issue response task - get suspicious files",
		},
		"event.instruction.list_ps": {
			Description: func(m map[string]interface{}) string {
				return fmt.Sprintf("Successfully issued View Host Processes task, Response ID: %s", m["task_id"])
			},
			LogType: "operation log",
			Action:  "Issue response task - view host processes",
		},
		"event.instruction.process_dump": {
			Description: func(m map[string]interface{}) string {
				return fmt.Sprintf("Successfully issued Download process Dump file task, Response ID: %s", m["task_id"])
			},
			LogType: "operation log",
			Action:  "Issue response task - download process dump file",
		},
		"event.instruction.image_analyze": {
			Description: func(m map[string]interface{}) string {
				return fmt.Sprintf("Successfully issued View module analysis task, Response ID: %s", m["task_id"])
			},
			LogType: "operation log",
			Action:  "Issue response task - view module analysis",
		},
		"event.instruction.process_analyze": {
			Description: func(m map[string]interface{}) string {
				return fmt.Sprintf("Successfully issued View process analysis task, Response ID: %s", m["task_id"])
			},
			LogType: "operation log",
			Action:  "Issue response task - view process analysis",
		},
		"event.instruction.quick_malware_scan": {
			Description: func(m map[string]interface{}) string {
				return fmt.Sprintf("Successfully issued Rapid scan task, Scan ID: %s", m["task_id"])
			},
			LogType: "operation log",
			Action:  "Issue scan task - rapid scan",
		},
		"event.instruction.full_malware_scan": {
			Description: func(m map[string]interface{}) string {
				return fmt.Sprintf("Successfully issued Full scan task, Scan ID: %s", m["task_id"])
			},
			LogType: "operation log",
			Action:  "Issue scan task - full scan",
		},
		"event.instruction.custom_malware_scan": {
			Description: func(m map[string]interface{}) string {
				return fmt.Sprintf("Successfully issued Customized path scanning task, Scan ID: %s", m["task_id"])
			},
			LogType: "operation log",
			Action:  "Issue scan task - customized path scanning",
		},
		"event.instruction.end_scan": {
			Description: func(m map[string]interface{}) string {
				return fmt.Sprintf("Successfully terminated scan task, Task ID: %s", m["task_id"])
			},
			LogType: "operation log",
			Action:  "Terminate scan task",
		},
		"event.isolate_file.release": {
			Description: func(m map[string]interface{}) string {
				return fmt.Sprintf("Successfully released quarantined file.")
			},
			Action:  "Release quarantined file",
			LogType: "operation log",
		},
		"event.isolate_file.delete": {
			Description: func(m map[string]interface{}) string {
				return fmt.Sprintf("Successfully deleted quarantined file.")
			},
			Action:  "Delete quarantined file",
			LogType: "operation log",
		},
		"event.ioc.add": {
			Description: func(m map[string]interface{}) string {
				if m["action"] == "Allow" {
					return fmt.Sprintf("The hash “%s”has been added to the IOC exclusions", m["hash"])
				}
				return fmt.Sprintf("The hash “%s”has been added to the IOC exclusions block", m["hash"])
			},
			LogType: "operation log",
			Action:  "Add IOC",
		},
		"event.ioc.delete": {
			Description: func(m map[string]interface{}) string {
				return fmt.Sprintf("Successfully deleted IOC success")
			},
			Action:  "Delete IOC",
			LogType: "operation log",
		},
		"event.ioc.update": {
			Description: func(m map[string]interface{}) string {
				return fmt.Sprintf("Successfully edited IOC.")
			},
			Action:  "Edit IOC",
			LogType: "operation log",
		},
		"event.ioa.add": {
			Description: func(m map[string]interface{}) string {
				return fmt.Sprintf("Successfully added IOA Exclusions, exclusions name: %s， added IOA name: %s", m["exclusion_name"], m["ioa_name"])
			},
			Action:  "Add IOA exclusions",
			LogType: "operation log",
		},
		"event.ioa.delete": {
			Description: func(m map[string]interface{}) string {
				return fmt.Sprintf("Successfully deleted IOA from the exclusions.")
			},
			Action:  "Delete IOA exclusions",
			LogType: "operation log",
		},
		"event.ioa.update": {
			Description: func(m map[string]interface{}) string {
				return fmt.Sprintf("Successfully edited IOA Exclusions, exclusions name: %s，added IOA name: %s", m["exclusion_name"], m["ioa_name"])
			},
			Action:  "Edit IOA exclusions",
			LogType: "operation log",
		},
		"event.notify.add": {
			Description: func(m map[string]interface{}) string {
				return fmt.Sprintf("Successfully added Notification Policies, policy name: %s", m["name"])
			},
			Action:  "Add notification policies",
			LogType: "operation log",
		},
		"event.notify.delete": {
			Description: func(m map[string]interface{}) string {
				return "Successfully deleted Notification Policies."
			},
			Action:  "Delete notification policies",
			LogType: "operation log",
		},
		"event.notify.update": {
			Description: func(m map[string]interface{}) string {
				return "Successfully edited Notification Policies."
			},
			Action:  "Edit notification policies",
			LogType: "operation log",
		},
		"event.robot.add": {
			Description: func(m map[string]interface{}) string {
				robotName := "DingTalk"
				if cast.ToInt(m["im_type"]) == NotifyImSettingImType2 {
					robotName = "Feishu"
				}
				if cast.ToInt(m["im_type"]) == NotifyImSettingImType3 {
					robotName = "WeCom"
				}

				return fmt.Sprintf("Successfully added %s IM Bot.", robotName)
			},
			LogType: "operation log",
			Action:  "Add IM bot",
		},
		"event.robot.delete": {
			Description: func(m map[string]interface{}) string { return "Successfully deleted IM Bot." },
			Action:      "Delete IM bot",
			LogType:     "operation log",
		},
		"event.robot.update": {
			Description: func(m map[string]interface{}) string {
				robotName := "DingTalk"
				if cast.ToInt(m["im_type"]) == NotifyImSettingImType2 {
					robotName = "Feishu"
				}
				if cast.ToInt(m["im_type"]) == NotifyImSettingImType3 {
					robotName = "WeCom"
				}

				return fmt.Sprintf("Successfully edited %s IM Bot, IM configuration name: %s", robotName, m["name"])
			},
			Action:  "Edit IM bot",
			LogType: "operation log",
		},
		"event.client_uninstall_password.on": {
			Description: func(m map[string]interface{}) string {
				return "Successfully enabled Uninstall Password configuration."
			},
			Action:  "Successfully enabled Uninstall Password configuration.",
			LogType: "operation log",
		},
		"event.client_uninstall_password.update": {
			Description: func(m map[string]interface{}) string {
				return "Successfully enabled Uninstall Password configuration."
			},
			Action:  "Enable uninstall password configuration",
			LogType: "operation log",
		},
		"event.client_uninstall_password.off": {
			Description: func(m map[string]interface{}) string {
				return "Successfully edited Uninstall Password."
			},
			Action:  "Modify uninstall password",
			LogType: "operation log",
		},
		"event.host.importance": {
			Description: func(m map[string]interface{}) string {
				if cast.ToInt(m["importance"]) == 1 {
					return fmt.Sprintf("Successfully changed host importance of Client ID: %s to 'Important Host'.", m["client_id"])
				}
				return fmt.Sprintf("Successfully changed host importance of Client ID: %s to 'Normal Host'.", m["client_id"])
			},
			Action:  "Change host importance",
			LogType: "operation log",
		},
		"event.detection.add": {
			Description: func(m map[string]interface{}) string {
				return fmt.Sprintf("Successfully added Hunting Detection, condition name: %s", m["name"])
			},
			Action:  "Add hunting detection",
			LogType: "operation log",
		},
		"event.detection.delete": {
			Description: func(m map[string]interface{}) string {
				return fmt.Sprintf("Successfully deleted Hunting Detection.")
			},
			Action:  "Delete hunting detection",
			LogType: "operation log",
		},
		"event.detection.update": {
			Description: func(m map[string]interface{}) string {
				return fmt.Sprintf("Successfully edited Hunting Detection, condition name: %s", m["name"])
			},
			Action:  "Edit hunting detection",
			LogType: "operation log",
		},
		"event.detection.export": {
			Description: func(m map[string]interface{}) string {
				return "Successfully exported Hunting Detection data."
			},
			Action:  "Export hunting detection data",
			LogType: "operation log",
		},
		"event.detection.status": {
			Description: func(m map[string]interface{}) string {
				status := "New"
				if cast.ToInt(m["deal_status"]) == 2 {
					status = "In Process"
				}
				if cast.ToInt(m["deal_status"]) == 3 {
					status = "Closed"
				}
				if cast.ToInt(m["deal_status"]) == 4 {
					status = "False Positive"
				}

				return fmt.Sprintf("Successfully handled %d behavior detections and updated status to “%s”", len(cast.ToStringSlice(m["ids"])), status)
			},
			Action:  "Dispose of behavior detection",
			LogType: "operation log",
		},
		"event.incident.status": {
			Description: func(m map[string]interface{}) string {
				status := "New"
				if cast.ToInt(m["status"]) == 2 {
					status = "In Process"
				}
				if cast.ToInt(m["status"]) == 3 {
					status = "Closed"
				}
				if cast.ToInt(m["status"]) == 4 {
					status = "False Positive"
				}
				totalDetection := cast.ToInt(m["total_detection"])
				totalIncident := cast.ToInt(m["total_incident"])
				incidentsName := strings.Join(cast.ToStringSlice(m["incident_names"]), ",")

				if totalDetection == 0 {
					return fmt.Sprintf("Successfully handled %d adversary incidents and updated status to ”%s“. All adversary incident names:%s", totalIncident, status, incidentsName)
				}

				return fmt.Sprintf("Successfully updated the status of %d adversary incidents and associated %d behavior detections to “%s”. All adversary incident names:%s", totalIncident, totalDetection, status, incidentsName)
			},
			Action:  "Dispose of adversary incident",
			LogType: "operation log",
		},
		"event.client.download": {
			Description: func(m map[string]interface{}) string {

				return fmt.Sprintf("Successfully downloaded Sensor, Agent Name: %s，Version Number: %s", m["file_name"], m["version"])
			},
			Action:  "Download sensor",
			LogType: "operation log",
		},
	}}
