package consts

import "sase-client-info/internal/config"

const (
	CompanyQax = "qax"
	CompanyRm  = "rm"

	EdrConsolePrefix = "/api/v1/console_edr"
	EdrReplacePath1  = "/api/v1/overseas"
	EdrReplacePath2  = "/api/v1"

	LanguageZh = "zh"
	LanguageEn = "en"

	ChannelRM             = "rm"
	ChannelTianShou       = "ts"
	ChannelTianShouPure   = "ts_pure"
	ChannelTianYan        = "ty"
	ChannelRooma          = "rm"
	ChannelRoomaApi       = "rmapi"
	ChannelTianShouZhPure = "ts_zh_pure"
	ChannelRmSase         = "rm_sase"

	VerifyCodeSource1   = "instruction_policy"
	VerifyCodeSource2   = "uninst_agent"
	VerifyCodeSource3   = "setting_verify_code"
	VerifyCodeSource4   = "instruction"
	VerifyCodeSource5   = "show_uninstall_pass"
	VerifyCodeSource6   = "switch_uninstall_pass"
	VerifyCodeSource7   = "update_uninstall_pass"
	VerifyCodeSource8   = "powershell"
	VerifyCodeSource9   = "host_file"
	VerifyCodeSourceTen = "host_registry"

	VerifyCodeInterval              = "sase_verify_code_%s_%s_%s_interval"
	VerifyCodeValidTime             = "sase_verify_code_%s_%s_%s_valid_time"
	VerifyCodeTimesDayForPhoneEmail = "sase_verify_code_%s_%s_times_day"
	VerifyCodeTimesDayForIp         = "sase_verify_code_%s_%s_times_day_for_ip"

	VerIfyCodeLastTime = "sase_verify_code_%s_%s_last_time" // 上一次验证还是否有效

	VerifyCodeSwitchOpen  = "open"
	VerifyCodeSwitchClose = "close"
)

var (
	EmailVerificationCodeTmp = `
<div>
<p>Hello，[%v],</p>
<p>We have received your request for a verification code to access your ` + config.Get().Server.CompanyBrandName + ` account.</p>
<p>Your verification code is: </p>
<h2>[%s]</h2>
<p>If you did not request this code, you can safely ignore this email. Someone may have mistakenly entered your email address.</p>
<p>Thank you!</p>
<p>Best regards,</p>
<p> ` + config.Get().Server.CompanyBrandName + ` Team</p>
</div>
`
)

var VerifyCodeSourceMap = map[string]string{
	VerifyCodeSource1:   VerifyCodeSource1,
	VerifyCodeSource2:   VerifyCodeSource2,
	VerifyCodeSource3:   VerifyCodeSource3,
	VerifyCodeSource4:   VerifyCodeSource4,
	VerifyCodeSource5:   VerifyCodeSource5,
	VerifyCodeSource6:   VerifyCodeSource6,
	VerifyCodeSource7:   VerifyCodeSource7,
	VerifyCodeSource8:   VerifyCodeSource8,
	VerifyCodeSource9:   VerifyCodeSource9,
	VerifyCodeSourceTen: VerifyCodeSourceTen,
}

const (
	GetProcessList             = "list_ps"
	KillProcess                = "kill_ps"
	BatchKillProcess           = "batch_kill_ps"
	ProcessAnalyze             = "process_analyze"
	ImageAnalyze               = "image_analyze"
	QuarantineFile             = "quarantine_file"
	BatchQuarantineFile        = "batch_quarantine_file"
	RecoverFile                = "recover_file"
	QuarantineNetwork          = "quarantine_network"
	RecoverNetwork             = "recover_network"
	ProcessDump                = "process_dump"
	RecoverQuarantineFile      = "recover_quarantine_file"
	DeleteQuarantineFile       = "delete_quarantine_file"
	BatchRecoverQuarantineFile = "batch_recover_quarantine_file"
	BatchDeleteQuarantineFile  = "batch_delete_quarantine_file"
	GetSuspiciousFile          = "get_suspicious_file"
	FullMalwareScan            = "full_malware_scan"
	QuickMalwareScan           = "quick_malware_scan"
	CustomMalwareScan          = "custom_malware_scan"
	UninstAgent                = "uninst_agent"
)

const (
	PassportRedisKey = "passport_tonken_%d_%d"
)
