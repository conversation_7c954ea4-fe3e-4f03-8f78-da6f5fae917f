package errors

type ErrorCode struct {
	Code      int
	Message   string
	MessageEn string
}

func (e ErrorCode) Error() string {
	return e.Message
}

func New(code int, message string, messageEn string) ErrorCode {
	return ErrorCode{Code: code, Message: message, MessageEn: messageEn}
}

var (
	Success          = New(0, "success", "success")
	ParamsError      = New(110001, "参数错误", "Parameter error")
	InvalidParameter = New(110003, "无效参数", "Invalid parameter")
	AddError         = New(110018, "程序异常", "program exception")
	DeleteError      = New(110019, "程序异常", "program exception")
	UpdateError      = New(110020, "程序异常", "program exception")
	SelectError      = New(110021, "程序异常", "program exception")
	ConnectionError  = New(110022, "程序异常", "program exception")

	PassportForbid           = New(170001, "禁止访问", "Access denied")
	PassportGetUserError     = New(170002, "获取用户信息失败", "Failed to Retrieve User Information")
	PassportGetUser2UidError = New(170003, "检测失败", "Detection failed") // uid <= 0
	PassportLogoutError      = New(170004, "退出失败", "Logout failed")

	NoAccess                   = New(260001, "没有权限", "No permission")
	UserLoginError             = New(260002, "登录失败", "Login failed")
	UserLoginPasswdError       = New(260003, "用户名称或密码错误", "Username or password is incorrect")
	UserIsDisabled             = New(260004, "用户已禁用，请联系管理员", "User is disabled, please contact the administrator")
	UserOverstepLoginFailLimit = New(260005, "登录失败次数过多，用户已禁用，请联系管理员", "Login failed too many times, the user has been disabled, please contact administrator")
	UserJWTCheckFail           = New(260006, "授权失败", "Authorization failed")
	UserJWTExpired             = New(260007, "令牌已过期", "Token has expired")
	UserJWTDeleted             = New(260008, "您已在其他地方登录，如不是本人，请及时修改密码！", "You have logged in elsewhere. If this is not you, please change your password immediately!")
	UserLoginCreateTokenError  = New(260009, "创建令牌错误", "Creating token error")
	UserPermissionsError       = New(260010, "没有足够的权限", "Insufficient permissions")
)

var (
	VerifyCodeTimeDayError      = New(710001, "请稍后再试", "Please try again later.")
	VerifyCodeTimeDayForIpError = New(710002, "请稍后再试", "Please try again later.")                             // 短信发送次数超出每天发送限制
	VerifyCodeCreateError       = New(710003, "生成验证码错误", "The generation of the verification code is error.") //
	VerifyCodeIntervalError     = New(710004, "2分钟之内不能重复请求验证码", "You cannot request the verification code again within two minutes.")
	VerifyCodeSetTimesError     = New(710005, "请稍后再试", "Please try again later.")                  // 服务器内部错误
	VerifyCodeSetValidCodeError = New(710006, "请稍后再试", "Please try again later.")                  // 服务器内部错误
	VerifyCodeValidationFailed  = New(710007, "验证码错误", "Verification code error")                  //
	VerifyCodeNotEmpty          = New(710008, "验证码不能为空", "The verification code cannot be empty.") //

)
