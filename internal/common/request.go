package common

import (
	"encoding/json"
	"fmt"
	"net/http/httputil"
	"net/url"
	"strings"
	"time"

	"sase-client-info/internal/common/consts"
	"sase-client-info/internal/config"
	"sase-client-info/internal/model"
	"sase-client-info/internal/resource"
	"sase-client-info/pkg/utils/helper"

	"github.com/gin-gonic/gin"
)

// DecodeJsonRequest 解码JSON请求
func DecodeJsonRequest(ctx *gin.Context, v interface{}) error {
	if err := ctx.ShouldBind(v); err != nil {
		return err
	}

	return nil
}

// ProxyCloudRequest 代理请求
func ProxyCloudRequest(ctx *gin.Context) {
	cloudApi := config.Get().Server.SaseCloudApi
	productId := ctx.GetString("product_id")
	userId := ctx.GetString("user_id")
	userName := ctx.GetString("user_name")

	u, _ := url.Parse(cloudApi)
	devTest := config.Get().DevTest
	if devTest.Switch {
		productId = devTest.SaseProductId
		userId = devTest.UserId
		userName = devTest.UserName
	}

	path := ctx.Request.URL.Path
	fmt.Println(path, cloudApi)
	proxy := httputil.NewSingleHostReverseProxy(u)
	ctx.Request.Header.Set("X-UserId", userId)
	ctx.Request.Header.Set("X-UserName", userName)
	ctx.Request.Header.Set("X-ProductId", productId)
	ctx.Request.Header.Set("X-From", "sase-proxy")
	ctx.Request.Host = u.Host
	ctx.Request.URL.Scheme = u.Scheme
	ctx.Request.URL.Host = u.Host
	ctx.Request.URL.Path = path

	// 这个地方添加处理
	go registerAccessLog(ctx, path, cloudApi, userId, productId, userName)

	proxy.ServeHTTP(ctx.Writer, ctx.Request)
}

// ProxyCloudConnectorClusterService 代理请求 connector-cluster-service服务
func ProxyCloudConnectorClusterService(ctx *gin.Context) {
	cloudApi := config.Get().Server.SaseCloudApi
	productId := ctx.GetString("product_id")
	userId := ctx.GetString("user_id")
	userName := ctx.GetString("user_name")
	ctx.Set("channel", consts.ChannelRmSase)
	u, _ := url.Parse(cloudApi)
	devTest := config.Get().DevTest
	if devTest.Switch {
		productId = devTest.SaseProductId
		userId = devTest.UserId
		userName = devTest.UserName
	}

	path := strings.Replace(ctx.Request.URL.Path, config.Get().Server.SaseContext, "connector_cluster", 1)
	fmt.Println(cloudApi, path)
	proxy := httputil.NewSingleHostReverseProxy(u)
	ctx.Request.Header.Set("X-UserId", userId)
	ctx.Request.Header.Set("X-UserName", userName)
	ctx.Request.Header.Set("X-ProductId", productId)
	ctx.Request.Header.Set("X-From", "sase-proxy")
	ctx.Request.Host = u.Host
	ctx.Request.URL.Scheme = u.Scheme
	ctx.Request.URL.Host = u.Host
	ctx.Request.URL.Path = path

	// 这个地方添加处理
	go registerAccessLog(ctx, path, cloudApi, userId, productId, userName)

	proxy.ServeHTTP(ctx.Writer, ctx.Request)
}

// ProxyCloudSoftwareManagerClusterService 代理请求 connector-cluster-service服务
func ProxyCloudSoftwareManagerClusterService(ctx *gin.Context) {
	cloudApi := config.Get().Server.SaseCloudApi
	productId := ctx.GetString("product_id")
	userId := ctx.GetString("user_id")
	userName := ctx.GetString("user_name")
	ctx.Set("channel", consts.ChannelRmSase)
	u, _ := url.Parse(cloudApi)
	devTest := config.Get().DevTest
	if devTest.Switch {
		productId = devTest.SaseProductId
		userId = devTest.UserId
		userName = devTest.UserName
	}

	path := strings.Replace(ctx.Request.URL.Path, config.Get().Server.SaseContext, "software_manager", 1)
	fmt.Println(cloudApi, path)
	proxy := httputil.NewSingleHostReverseProxy(u)
	ctx.Request.Header.Set("X-UserId", userId)
	ctx.Request.Header.Set("X-UserName", userName)
	ctx.Request.Header.Set("X-ProductId", productId)
	ctx.Request.Header.Set("X-From", "sase-proxy")
	ctx.Request.Host = u.Host
	ctx.Request.URL.Scheme = u.Scheme
	ctx.Request.URL.Host = u.Host
	ctx.Request.URL.Path = path
	go registerAccessLog(ctx, path, cloudApi, userId, productId, userName)
	proxy.ServeHTTP(ctx.Writer, ctx.Request)
}

// ProxySaseRequest 代理请求
func ProxySaseRequest(ctx *gin.Context) {
	saseDcsApi := ctx.GetString("dcs_api")
	productId := ctx.GetString("product_id")
	userId := ctx.GetString("user_id")
	userName := ctx.GetString("user_name")
	ctx.Set("channel", consts.ChannelRmSase)

	devTest := config.Get().DevTest
	if devTest.Switch {
		saseDcsApi = devTest.SaseDcsApi
		productId = devTest.EdrProductId
		userId = devTest.UserId
		userName = devTest.UserName
	}

	u, _ := url.Parse(saseDcsApi)

	path := strings.Replace(strings.Replace(ctx.Request.URL.Path, config.Get().Server.SaseContext, "", 1), "/api", "/api2", 1)

	proxy := httputil.NewSingleHostReverseProxy(u)
	ctx.Request.Header.Set("X-UserId", userId)
	ctx.Request.Header.Set("X-UserName", userName)
	ctx.Request.Header.Set("X-ProductId", productId)
	ctx.Request.Header.Set("X-From", "sase-proxy")
	ctx.Request.Host = u.Host
	ctx.Request.URL.Scheme = u.Scheme
	ctx.Request.URL.Host = u.Host
	ctx.Request.URL.Path = path

	// 这个地方添加处理
	go registerAccessLog(ctx, path, saseDcsApi, userId, productId, userName)

	proxy.ServeHTTP(ctx.Writer, ctx.Request)
}

// ProxyEdrRequest 代理EDR请求
func ProxyEdrRequest(ctx *gin.Context) {
	productId := ctx.GetString("product_id")
	edrDcsApi := ctx.GetString("dcs_api")
	userId := ctx.GetString("user_id")
	userName := ctx.GetString("user_name")
	ctx.Set("channel", consts.ChannelRmSase)

	devTest := config.Get().DevTest
	if devTest.Switch {
		productId = devTest.EdrProductId
		edrDcsApi = devTest.EdrDcsApi
		userId = devTest.UserId
		userName = devTest.UserName
	}

	u, _ := url.Parse(edrDcsApi)

	var lastPath string
	path := strings.Replace(ctx.Request.URL.Path, config.Get().Server.SaseContext, "", 1)

	path2 := strings.Replace(path, consts.EdrConsolePrefix, consts.EdrReplacePath2, 1)

	if _, ok := edrRequestPath[path2]; ok {
		lastPath += path2
	} else {
		lastPath += strings.Replace(path, consts.EdrConsolePrefix, consts.EdrReplacePath1, 1)
	}

	if source, ok := VerifyCodePath[strings.Replace(ctx.Request.URL.Path, config.Get().Server.SaseContext, "", 1)]; ok {
		err := model.VerifyCodeRepo.CheckVerifyCode(ctx, source)
		if err != nil {
			ResponseResult(ctx, err)
			return
		}
		fmt.Println("----------------", lastPath)
		if helper.InArray(source, []string{consts.VerifyCodeSource5, consts.VerifyCodeSource6, consts.VerifyCodeSource7, consts.VerifyCodeSource4}) {
			lastPath = strings.Replace(lastPath, "/v1", "/v2", 1)
		}
	}

	proxy := httputil.NewSingleHostReverseProxy(u)
	ctx.Request.Header.Set("X-UserId", userId)
	ctx.Request.Header.Set("X-UserName", userName)
	ctx.Request.Header.Set("X-OrgName", productId)
	ctx.Request.Header.Set("X-From", "sase-proxy")
	ctx.Request.Header.Set("X-Channel", consts.ChannelRmSase)
	ctx.Request.Host = u.Host
	ctx.Request.URL.Scheme = u.Scheme
	ctx.Request.URL.Host = u.Host
	ctx.Request.URL.Path = lastPath
	ctx.Set("path", lastPath)
	proxy.ServeHTTP(ctx.Writer, ctx.Request)
}

var VerifyCodePath = map[string]string{
	"/api/v1/console_edr/settings/get_uninstall_conf":      consts.VerifyCodeSource5,
	"/api/v1/console_edr/settings/switch_uninstall_status": consts.VerifyCodeSource6,
	"/api/v1/console_edr/settings/update_uninstall_pass":   consts.VerifyCodeSource7,
	"/api/v1/console_edr/instructions/send_instruction":    consts.VerifyCodeSource4,
	"/api/v1/console_edr/instructions/uninstall_client":    consts.VerifyCodeSource2,
	"/api/v1/console_edr/instruction_policy/save_status":   consts.VerifyCodeSource1,
}

// edrRequestPath 替换edr请求路径
var edrRequestPath = map[string]int{
	"/api/v1/virus_scan/add":                            1,
	"/api/v1/virus_scan/update":                         1,
	"/api/v1/virus_scan/cancel":                         1,
	"/api/v1/virus_scan/list":                           1,
	"/api/v1/virus_scan/scan_record":                    1,
	"/api/v1/hosts/statistics":                          1,
	"/api/v1/hosts/list":                                1,
	"/api/v1/hosts/update":                              1,
	"/api/v1/hosts/filter_client_version":               1,
	"/api/v1/hosts/filter_os_version":                   1,
	"/api/v1/hosts/detail":                              1,
	"/api/v1/instructions/send_instruction":             1,
	"/api/v1/instructions/tasks":                        1,
	"/api/v1/instructions/task_notice":                  1,
	"/api/v1/instructions/retry_instruction":            1,
	"/api/v1/instructions/task_result":                  1,
	"/api/v1/file_upload/download":                      1,
	"/api/v1/instructions/get_batch_instruction_result": 1,
	"/api/v1/instructions/end_scan":                     1,
	"/api/v1/instructions/update_status":                1,
	"/api/v1/instructions/scan_tasks":                   1,
	"/api/v1/instructions/uninstall_client":             1,
	"/api/v2/instructions/send_instruction":             1,
	"/api/v1/instructions/batch_instruction":            1,
	"/api/v1/notify/get_message_list":                   1,
	"/api/v1/notify/add_setting":                        1,
	"/api/v1/notify/get_im_setting_list":                1,
	"/api/v1/notify/send_test_message":                  1,
	"/api/v1/notify/save_status_or_delete":              1,
	"/api/v1/notify/update":                             1,
	"/api/v1/notify_policy/update":                      1,
	"/api/v1/notify_policy/add_policy":                  1,
	"/api/v1/notify_policy/update_status":               1,
	"/api/v1/notify_policy/delete":                      1,
	"/api/v1/notify_policy/get_list":                    1,
	"/api/v1/robot/weekly_report/download":              1,
	"/api/v1/robot/weekly_report/cycle/list":            1,
	"/api/v1/robot/weekly_report/info":                  1,
	"/api/v1/robot/block_log/list":                      1,
	"/api/v1/guide/kill_ps_confirm":                     1,
	"/api/v1/guide/kill_ps_confirm_status":              1,
	"/api/v1/instruction_policy/add_policy":             1,
	"/api/v1/instruction_policy/list":                   1,
	"/api/v1/instruction_policy/save_sort":              1,
	"/api/v1/instruction_policy/delete":                 1,
	"/api/v1/instruction_policy/update":                 1,
	"/api/v1/instruction_policy/save_status":            1,
	"/api/v1/common/filter/get_t_list":                  1,
	"/api/v1/common/filter/get_ta_list":                 1,
	"/api/v1/common/filter/get_all_t_list":              1,
	"/api/v1/hosts/filter_hosts":                        1,
	"/api/v1/detection/alarms/engines_rule/get_ta":      1,
	"/api/v1/language/get_list":                         1,
	"/api/v1/detection/events/list":                     1,
	"/api/v1/detection/events/mappings":                 1,
	"/api/v1/detection/events/chart":                    1,
	"/api/v1/detection/events/column/value":             1,
	"/api/v1/detection/events/log_rule/save":            1,
	"/api/v1/detection/events/log_rule/list":            1,
	"/api/v1/detection/events/log_rule/update":          1,
	"/api/v1/detection/events/log_rule/delete":          1,
	"/api/v1/detection/alarms/events_log/alarm_list":    1,
	"/api/v1/files/upgrade/guide_pkg":                   1,
	"/api/v1/stat/deivce/overview":                      1,
	"/api/v1/stat/deivce/os_version":                    1,
}

func registerAccessLog(gCtx *gin.Context, path, saseDcsApi, userId, productId, userName string) {
	ctx := gCtx.Copy()
	var bodyMap = make(map[string]interface{})
	bodyByte, err := ctx.GetRawData()
	if err == nil {
		_ = json.Unmarshal(bodyByte, &bodyMap)
	}
	accessField := helper.New(
		helper.WithViewType(1),
		helper.WithPath(path),
		helper.WithUId(userId),
		helper.WithFrom(saseDcsApi),
		helper.WithTime(time.Now().Unix()),
		helper.WithIp(helper.GetClientIp(ctx)),
		helper.WithChannel(consts.ChannelRmSase),
		helper.WithOrgName(productId),
		helper.WithRequest(bodyMap),
		helper.WithHeader(map[string]interface{}{
			"X-UserId":   userId,
			"X-UserName": userName,
			"X-OrgName":  productId,
			"X-Channel":  consts.ChannelRmSase,
			"X-From":     consts.ChannelRmSase,
		}),
		helper.WithHttpMethod(ctx.Request.Method),
		helper.WithOperator(userName),
	)

	resource.LogRecorder.Access(accessField)

}
