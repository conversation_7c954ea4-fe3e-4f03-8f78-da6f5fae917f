package dto

// DepartmentRoleResponse 部门角色响应结构体
type DepartmentRoleResponse struct {
	RoleCode string `json:"role_code"`
	RoleName string `json:"role_name"`
	CreateAt int64  `json:"create_at,omitempty"`
	UpdateAt int64  `json:"update_at,omitempty"`
}

// DepartmentRoleListResponse 部门角色列表响应结构体
type DepartmentRoleListResponse struct {
	Total int                      `json:"total"`
	Items []DepartmentRoleResponse `json:"items"`
}
