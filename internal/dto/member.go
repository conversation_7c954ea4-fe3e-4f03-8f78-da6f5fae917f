package dto

// MemberResponse 成员响应结构体
type MemberResponse struct {
	UserCode              string   `json:"user_code"`
	UserName              string   `json:"user_name"`
	Account               string   `json:"account"`
	OrgCode               string   `json:"org_code"`
	DeptCode              string   `json:"dept_code"`
	DeptPath              []string `json:"dept_path"`
	SourceType            string   `json:"source_type"`
	SourceCode            string   `json:"source_code"`
	Mobile                string   `json:"mobile"`
	Email                 string   `json:"email"`
	Position              string   `json:"position"`
	ValidTime             int64    `json:"valid_time"`
	IsActivationEmailSent bool     `json:"is_activation_email_sent"`
	GroupIds              string   `json:"group_ids"`
	Status                int      `json:"status,omitempty"`
	BindDeviceStatus      int      `json:"bind_device_status,omitempty"`
	CreateAt              int64    `json:"create_at"`
	UpdateAt              int64    `json:"update_at"`
}

// MemberListResponse 成员列表响应结构体
type MemberListResponse struct {
	Total int              `json:"total"`
	Items []MemberResponse `json:"items"`
}

type SimpleDepartmentMemberResponse struct {
	UserCode string   `json:"user_code"` // 用户编码
	UserName string   `json:"user_name"` // 用户名
	DeptCode string   `json:"dept_code"` // 部门编码
	DeptPath []string `json:"dept_path"` // 部门路径
	RoleCode string   `json:"role_code"` // 角色编码
}

// SimpleSearchDepartmentMemberResponse 简单搜索用户响应
type SelectDepartmentMemberResponse struct {
	Total int64                            `json:"total"`
	Items []SimpleDepartmentMemberResponse `json:"items"`
}
