package dto

type VerifyCodeSendRequest struct {
	Source string `json:"source"`
}
type VerifyCodeIsNeedVerifyRequest struct {
	Source string `json:"source"`
}

type VerifyCodeIsNeedVerifyResponse struct {
	IsNeedVerifyCode bool   `json:"is_need_verify_code"`
	PhoneEmail       string `json:"phone_email"`
	I                int64  `json:"-"`
}

type ConnectHostCheckVerifyCode struct {
	Source     string `json:"source"`
	VerifyCode string `json:"verify_code"`
}
