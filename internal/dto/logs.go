package dto

type AuditLogListRequest struct {
	Search     string  `json:"name" form:"name"`
	StartTime  int64   `json:"start_time" form:"start_time"`
	Ip         string  `json:"ip" form:"ip"`
	Action     string  `json:"action" form:"action"`
	LogType    string  `json:"log_type" form:"log_type"`
	IsExport   int     `json:"is_export" form:"is_export"`
	Channel    string  `json:"channel" form:"channel"`
	Describe   string  `json:"describe" form:"describe"`
	UserName   string  `json:"user_name" form:"user_name"`
	EndTime    int64   `json:"end_time" form:"end_time"`
	Page       int64   `json:"page" binding:"min=1"`
	Limit      int64   `json:"limit" binding:"max=1000"`
	CreateTime []int64 `json:"time"`
}

type EsAuditLog struct {
	UserName string `json:"user_name"`
	UId      string `json:"uid"`
	Path     string `json:"path"`
	//Request    interface{} `json:"request.body"`
	Payload    interface{} `json:"payload"`
	LogType    string      `json:"log_type"`
	Time       int64       `json:"time"`
	Action     string      `json:"action"`
	ActionType string      `json:"action_type"`
	Operator   string      `json:"operator"`
	Event      string      `json:"event"`
	Ip         string      `json:"ip"`
	Id         string      `json:"id"`
}

type ESLog struct {
	RmMessage *EsAuditLog `json:"rm_message"`
}

type AuditLogListResp struct {
	Results []*EsAuditLog `json:"items"`
	Total   int64         `json:"total"`
}
