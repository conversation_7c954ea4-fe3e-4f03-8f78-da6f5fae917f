package dto

// PassportGetJumpUrlRequest 通行证获取跳转URL请求
type PassportGetJumpUrlRequest struct {
	Uuid           string `form:"uuid" json:"uuid" binding:"required"`
	JumpType       string `form:"jump_type" json:"jump_type"`
	ConsoleJumpUrl string `form:"console_jump_url" json:"console_jump_url"`
}

// PassportVerifyTokenRequest 通行证验证Token请求
type PassportVerifyTokenRequest struct {
	Token string `form:"token" json:"token" binding:"required"`
	Uuid  string `form:"uuid" json:"uuid" binding:"required"`
}

type PassportResponse struct {
	Error   int         `json:"error"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}

type AuthLoginResp struct {
	AccessToken string `json:"access_token"`
	ProductId   string `json:"product_id"`
	UserName    string `json:"user_name"`
	UserId      string `json:"user_id"`
	IsAdmin     int    `json:"is_admin"`
}

type Pages struct {
	Page  int `json:"page"`
	Limit int `json:"limit"`
}

type ExternalGetUserOrgAppListByUserId struct {
}

type ExternalReceiveInvitationList struct {
	Pages
}

type ExternalUpdateReceiveInvitationStatus struct {
	Status int `json:"status"`
	Id     int `json:"id"`
}

type ExternalGetOrgMemberData struct {
	Pages
}

type ExternalInsertMember struct {
	Phone    string                            `json:"phone"`
	Email    string                            `json:"email"`
	Products map[string]map[string]interface{} `json:"products"`
}

type ExternalRemoveOrgUser struct {
	UserId int64 `json:"user_id"`
}

type ExternalGetProductRoles struct {
}

type ExternalGetUserOrgApp struct {
	UserId int64 `json:"user_id"`
}

type ExternalUpdateUserOrgAppRole struct {
	UserId   int                               `json:"user_id"`
	Products map[string]map[string]interface{} `json:"products"`
}

type ExternalInvitationList struct {
	Pages
}

type ExternalUpdateInvitationStatus struct {
	Status int `json:"status"`
	Id     int `json:"id"`
}

// V1GetMessageList 获取消息列表接口
type ExternalGetMessageList struct {
	ListType string `json:"list_type"`
	MsgType  int    `json:"msg_type"`
	Pages
}

type ExternalGetUnreadNum struct {
}

// V1MarkRead 标记消息为已读
type ExternalMarkRead struct {
	Ids []int64 `json:"ids"`
}

// V1MarkRead 标记消息为已读
type ExternalMessageDetail struct {
	Id int64 `json:"id"`
}

type ExternalSwitchOrgName struct {
	ProductId string `json:"product_id"`
}

type ExternalGetOrgAppInfoByOrgName struct {
	ProductId string `json:"product_id"`
}

type ExternalGetOrgAppInfoByOrgNameResponse struct {
	ApiKey         string `json:"api_key"`
	ApiSecret      string `json:"api_secret"`
	DcsApi         string `json:"dcs_api"`
	Addr           string `json:"addr"`
	AIAPI          string `json:"ai_api"`
	OrgId          int64  `json:"org_id"`
	BuyType        int    `json:"buy_type"`
	ClientCount    int    `json:"client_count"`
	PermissionData string `json:"permission_data"`
	StartTime      int64  `json:"start_time"`
	ExpireTime     int64  `json:"expire_time"`
}
