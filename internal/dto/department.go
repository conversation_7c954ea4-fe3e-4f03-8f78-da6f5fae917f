package dto

// DepartmentResponse 部门响应结构体
type DepartmentResponse struct {
	DeptCode       string `json:"dept_code"`
	DeptName       string `json:"dept_name"`
	OrgCode        string `json:"org_code"`
	ParentDeptCode string `json:"parent_dept_code"`
	HasSubDept     bool   `json:"has_sub_dept,omitempty"`
}

// DepartmentListResponse 部门列表响应结构体
type DepartmentListResponse struct {
	Total int                  `json:"total"`
	Items []DepartmentResponse `json:"items"`
}
