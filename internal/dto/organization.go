package dto

// OrganizationResponse 组织响应结构体
type OrganizationResponse struct {
	OrgCode           string                 `json:"org_code"`
	OrgName           string                 `json:"org_name"`
	SourceType        string                 `json:"source_type"`
	SourceConfig      map[string]interface{} `json:"source_config"`
	ProductID         string                 `json:"product_id"`
	SyncStatus        string                 `json:"sync_status"`
	LastSyncTime      *int64                 `json:"last_sync_time"`
	LastUpdateBatchNo string                 `json:"last_update_batch_no"`
	Status            int                    `json:"status"`
}

// OrganizationListResponse 组织列表响应结构体
type OrganizationListResponse struct {
	Total int                    `json:"total"`
	Items []OrganizationResponse `json:"items"`
}
