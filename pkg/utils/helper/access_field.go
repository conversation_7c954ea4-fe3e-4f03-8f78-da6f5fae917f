package helper

import (
	"sase-client-info/internal/config"
)

type Option func(op *AccessField) *AccessField

type AccessField struct {
	OrgName    string      `json:"org_name"`
	UserName   string      `json:"user_name"`
	UId        string      `json:"uid"`
	Path       string      `json:"path"`
	Request    interface{} `json:"request.body"`
	From       interface{} `json:"from"`
	Header     interface{} `json:"header"`
	IndexName  string      `json:"rm_nb_servers_index_name"`
	LogType    string      `json:"log_type"`
	Root       string      `json:"root"`
	Channel    string      `json:"channel"`
	Service    string      `json:"service"`
	Time       int64       `json:"time"`
	Action     string      `json:"action"`
	ActionType string      `json:"action_type"`
	Payload    interface{} `json:"payload"`
	Operator   string      `json:"operator"`
	Event      string      `json:"event"`
	Response   interface{} `json:"response"`
	Ip         string      `json:"ip"`
	ViewType   int         `json:"view_type"` // 0:常规的操作日志 1:审计日志
	HttpMethod string      `json:"http_method"`
}

func New(opts ...Option) *AccessField {

	access := &AccessField{Service: config.Get().Server.Name}

	for _, opt := range opts {
		access = opt(access)
	}

	return access
}

func WithOrgName(orgName string) Option {
	return func(op *AccessField) *AccessField {
		op.OrgName = orgName
		return op
	}
}

func WithLogType(logType string) Option {
	return func(op *AccessField) *AccessField {
		op.LogType = logType
		return op
	}
}

func WithUserName(userName string) Option {
	return func(op *AccessField) *AccessField {
		op.UserName = userName
		return op
	}
}

func WithUId(uId string) Option {
	return func(op *AccessField) *AccessField {
		op.UId = uId
		return op
	}
}

func WithPath(path string) Option {
	return func(op *AccessField) *AccessField {
		op.Path = path
		return op
	}
}
func WithResponse(response interface{}) Option {
	if response == nil {
		response = map[string]interface{}{}
	}
	return func(op *AccessField) *AccessField {
		op.Response = response
		return op
	}
}

func WithRequest(request interface{}) Option {
	return func(op *AccessField) *AccessField {
		op.Request = request
		return op
	}
}

func WithFrom(from interface{}) Option {
	return func(op *AccessField) *AccessField {
		op.From = from
		return op
	}
}

func WithHeader(header interface{}) Option {
	return func(op *AccessField) *AccessField {
		op.Header = header
		return op
	}
}

func WithRoot(root string) Option {
	return func(op *AccessField) *AccessField {
		op.Root = root
		return op
	}
}

func WithChannel(channel string) Option {
	return func(op *AccessField) *AccessField {
		op.Channel = channel
		return op
	}
}

func WithTime(time int64) Option {
	return func(op *AccessField) *AccessField {
		op.Time = time
		return op
	}
}

func WithIndexName(index string) Option {
	return func(op *AccessField) *AccessField {
		op.IndexName = index
		return op
	}
}
func WithPayload(payload map[string]interface{}) Option {
	return func(op *AccessField) *AccessField {
		op.Payload = payload
		return op
	}
}

func WithOperator(operator string) Option {
	return func(op *AccessField) *AccessField {
		op.Operator = operator
		return op
	}
}

func WithEvent(event string) Option {
	return func(op *AccessField) *AccessField {
		op.Event = event
		return op
	}
}

func Action(action string) Option {
	return func(op *AccessField) *AccessField {
		op.Action = action
		return op
	}
}

func ActionType(actionType string) Option {
	return func(op *AccessField) *AccessField {
		op.ActionType = actionType
		return op
	}
}

func WithIp(ip string) Option {
	return func(op *AccessField) *AccessField {
		op.Ip = ip
		return op
	}
}

func WithViewType(viewType int) Option {
	return func(op *AccessField) *AccessField {
		op.ViewType = viewType
		return op
	}
}
func WithHttpMethod(method string) Option {
	return func(op *AccessField) *AccessField {
		op.HttpMethod = method
		return op
	}
}
