package helper

import (
	"encoding/json"
	"fmt"
	"github.com/gin-gonic/gin"
	"strings"
)

type Business string
type Operator string

const (
	ActionOfInstructionPolicy Business = "INSTRUCTION_POLICY"
	ActionOfInstruction       Business = "INSTRUCTION" // 响应
	ActionOfIsolateFile       Business = "ISOLATE_FILE"
	ActionOfUser              Business = "USER"

	ActionOfIOC Business = "IOC"
	ActionOfIOA Business = "IOA"

	ActionOfNotify Business = "NOTIFY"
	ActionOfRobot  Business = "ROBOT"

	ActionOfClientConfigPwd Business = "CLIENT_UNINSTALL_PASSWORD"

	ActionOfHost               Business = "HOST"
	ActionOfThreateningHunting Business = "THREATENING_HUNTING"
	ActionOfDetection          Business = "DETECTION"
	ActionOfIncident           Business = "INCIDENT"

	ActionOfClient        Business = "CLIENT"
	ActionsOfConfig       Business = "CLIENT_CONFIG"
	ActionOfHostConnected          = "HOST_CONNECTED"
)

const (
	OPERATOR_UPDATE Operator = "UPDATE"
	OPERATOR_DELETE Operator = "DELETE"

	OPERATOR_DOWNLAOD Operator = "DOWNLOAD"
	OPERATOR_ADD      Operator = "ADD"
	OPERATOT_LOGIN    Operator = "LOGIN"
	OPERATOR_LOGOUT   Operator = "LOGOUT"

	OPERATOR_CONFIG_OPEN          Operator = "ON"
	OPERATOR_CONFIG_CLOSE         Operator = "OFF"
	OPERATOR_ISOLATE_FILE_RELEASE Operator = "RELEASE"
	OPERATOR_LOG_REPORT           Operator = "LOG_REPORT"
)

type BusinessOperator struct {
	BusinessType Business
	Operator     Operator
	BaseEvent    string
	BuildEvent   bool
	ParseReq     bool
	ParseResp    bool
}

var AuditMap = map[string]*BusinessOperator{
	"/api/v1/instruction_policy/add_policy": &BusinessOperator{
		BusinessType: ActionOfInstructionPolicy,
		Operator:     OPERATOR_ADD,
		BaseEvent:    "event.instruction_policy.add",
	},
	"/api/v1/instruction_policy/update": {
		BusinessType: ActionOfInstructionPolicy,
		Operator:     OPERATOR_UPDATE,
		BaseEvent:    "event.instruction_policy.update",
	},
	"/api/v1/instruction_policy/save_status": {
		BaseEvent:    "event.instruction_policy.update",
		BusinessType: ActionOfInstructionPolicy,
		Operator:     OPERATOR_UPDATE,
		ParseResp:    true,
	},
	"/api/v1/instruction_policy/delete": {
		BusinessType: ActionOfInstructionPolicy,
		Operator:     OPERATOR_DELETE,
		BaseEvent:    "event.instruction_policy.delete",
		ParseResp:    true,
	},
	"/api/v1/instructions/send_instruction": {
		BusinessType: ActionOfInstruction,
		BaseEvent:    "event.instruction.",
		ParseReq:     true,
		BuildEvent:   true,
		ParseResp:    true,
	},
	"/api/v2/instructions/send_instruction": {
		BusinessType: ActionOfInstruction,
		BaseEvent:    "event.instruction.",
		ParseReq:     true,
		BuildEvent:   true,
		ParseResp:    true,
	},
	"/api/v1/instructions/end_scan": {
		BusinessType: ActionOfInstruction,
		BaseEvent:    "event.instruction.end_scan",
	},
	"/api/v1/passport/verify_token": {
		BusinessType: ActionOfUser,
		BaseEvent:    "event.user.login",
		Operator:     OPERATOT_LOGIN,
	},
	"/api/v1/auth/logout": {
		BusinessType: ActionOfUser,
		Operator:     OPERATOR_LOGOUT,
		BaseEvent:    "event.user.logout",
	},

	"/api/v1/overseas/isolate_file/release": {
		BusinessType: ActionOfIsolateFile,
		Operator:     OPERATOR_ISOLATE_FILE_RELEASE,
		BaseEvent:    "event.isolate_file.release",
		ParseResp:    true,
	},
	"/api/v1/overseas/isolate_file/delete": {
		BusinessType: ActionOfIsolateFile,
		Operator:     OPERATOR_DELETE,
		BaseEvent:    "event.isolate_file.delete",
		ParseResp:    true,
	},
	"/api/v1/overseas/configure/ioc/add": {
		BusinessType: ActionOfIOC,
		Operator:     OPERATOR_ADD,
		BaseEvent:    "event.ioc.add",
	},

	"/api/v1/overseas/configure/ioc/delete": {
		BusinessType: ActionOfIOC,
		Operator:     OPERATOR_DELETE,
		BaseEvent:    "event.ioc.delete",
		ParseResp:    true,
	},
	"/api/v1/overseas/configure/ioc/update": {
		BusinessType: ActionOfIOC,
		Operator:     OPERATOR_UPDATE,
		BaseEvent:    "event.ioc.update",
		ParseResp:    true,
	},

	"/api/v1/overseas/configure/ioa/delete": {
		BusinessType: ActionOfIOA,
		Operator:     OPERATOR_DELETE,
		BaseEvent:    "event.ioa.delete",
		ParseResp:    true,
	},
	"/api/v1/overseas/configure/ioa/update": {
		BusinessType: ActionOfIOA,
		Operator:     OPERATOR_UPDATE,
		BaseEvent:    "event.ioa.update",
		ParseResp:    true,
	},
	"/api/v1/overseas/configure/ioa/add": {
		BusinessType: ActionOfIOA,
		Operator:     OPERATOR_ADD,
		BaseEvent:    "event.ioa.add",
	},
	"/api/v1/notify_policy/add_policy": {
		BusinessType: ActionOfNotify,
		Operator:     OPERATOR_ADD,
		BaseEvent:    "event.notify.add",
	},
	"/api/v1/notify_policy/delete": {
		BusinessType: ActionOfNotify,
		Operator:     OPERATOR_DELETE,
		BaseEvent:    "event.notify.delete",
	},

	"/api/v1/notify_policy/update_status": {
		BusinessType: ActionOfNotify,
		Operator:     OPERATOR_UPDATE,
		BaseEvent:    "event.notify.update",
	},
	"/api/v1/notify_policy/update": {
		BusinessType: ActionOfNotify,
		Operator:     OPERATOR_UPDATE,
		BaseEvent:    "event.notify.update",
	},

	"/api/v1/notify/add_setting": {
		BusinessType: ActionOfRobot,
		Operator:     OPERATOR_ADD,
		BaseEvent:    "event.robot.add",
	},
	"/api/v1/notify/save_status_or_delete": {
		BusinessType: ActionOfRobot,
		Operator:     OPERATOR_UPDATE,
		BaseEvent:    "event.robot.delete",
		ParseResp:    true,
	},
	"/api/v1/notify/update": {
		BusinessType: ActionOfRobot,
		Operator:     OPERATOR_UPDATE,
		BaseEvent:    "event.robot.update",
	},
	"/api/v1/overseas/settings/switch_uninstall_status": {
		BusinessType: ActionOfClientConfigPwd,
		//Operator:     OPERATOR_CONFIG_OPEN,
		BaseEvent:  "event.client_uninstall_password.",
		ParseReq:   true,
		BuildEvent: true,
	},

	"/api/v1/overseas/settings/update_uninstall_pass": {
		BusinessType: ActionOfClientConfigPwd,
		Operator:     OPERATOR_UPDATE,
		BaseEvent:    "event.client_uninstall_password.update",
	},
	"/api/v1/overseas/hosts/update": {
		BusinessType: ActionOfHost,
		Operator:     OPERATOR_UPDATE,
		BaseEvent:    "event.host.importance",
		ParseResp:    true,
	},
	"/api/v1/detection/events/log_rule/save": {
		BusinessType: ActionOfThreateningHunting,
		Operator:     OPERATOR_ADD,
		BaseEvent:    "event.detection.add",
	},
	"/api/v1/detection/events/log_rule/update": {
		BusinessType: ActionOfThreateningHunting,
		Operator:     OPERATOR_UPDATE,
		BaseEvent:    "event.detection.update",
	},
	"/api/v1/detection/events/log_rule/delete": {
		BusinessType: ActionOfThreateningHunting,
		Operator:     OPERATOR_DELETE,
		BaseEvent:    "event.detection.delete",
	},
	"/api/v1/detection/alarms/events_log/alarm_list": {
		BusinessType: ActionOfThreateningHunting,
		Operator:     OPERATOR_UPDATE,
		BaseEvent:    "event.detection.export",
	},
	"/api/v1/overseas/detection/deal_status": {
		BusinessType: ActionOfDetection,
		Operator:     OPERATOR_UPDATE,
		BaseEvent:    "event.detection.status",
	},
	"/api/v1/overseas/incident/batch_deal": {
		BusinessType: ActionOfIncident,
		Operator:     OPERATOR_UPDATE,
		BaseEvent:    "event.incident.status",
		ParseResp:    true,
	},
	"/api/v1/files/upgrade/download": {
		BusinessType: ActionOfClient,
		BaseEvent:    "event.client.download",
		ParseResp:    true,
	},
	"/api/v1/report/log": {
		BusinessType: ActionOfHostConnected,
		ParseReq:     true,
		Operator:     OPERATOR_LOG_REPORT,
	},
}

type SwitchKey struct {
	SwitchKey    string `json:"switch_key"`
	SwitchStatus int    `json:"switch_status"`
	Value        int    `json:"value"`
}
type ClientConfig struct {
	ConfType   string       `json:"conf_type"`
	SwitchKeys []*SwitchKey `json:"switch_keys"`
}

type ClientUninstall struct {
	Switch string `json:"switch"` //on 是开 off是关
}

type HostConnected struct {
	HostName string `json:"host_name"`
	Event    string `json:"event"`
	ClientId string `json:"client_id"`
}
type HostImportance struct {
	Importance int `json:"importance"`
}

type Instruction struct {
	InstructionName string `json:"instruction_name"`
}
type InstructionResponse struct {
	TaskId string `json:"task_id"`
}

func BuildAccess(ctx *gin.Context, access *AccessField) {

	businessOp, _ := AuditMap[access.Path]
	access.Action = strings.ToLower(string(businessOp.Operator))

	access.ActionType = strings.ToLower(string(businessOp.BusinessType))
	access.LogType = "operator"

	if data, ok := access.Request.(map[string]interface{}); ok {
		access.Payload = data
	} else {
		access.Payload = map[string]interface{}{}
	}

	if businessOp.ParseReq {
		if businessOp.BusinessType == ActionOfInstruction {

			var conf *Instruction

			bytesData, _ := json.Marshal(access.Request)

			_ = json.Unmarshal(bytesData, &conf)
			access.Operator = conf.InstructionName
		}

		if businessOp.BusinessType == ActionOfClientConfigPwd {
			var conf *ClientUninstall

			bytesData, _ := json.Marshal(access.Request)

			_ = json.Unmarshal(bytesData, &conf)
			access.Operator = conf.Switch
		}

	}

	if !businessOp.BuildEvent {
		access.Event = businessOp.BaseEvent
		access.Operator = strings.ToLower(string(businessOp.Operator))
	} else {
		access.Event = fmt.Sprintf("%s%s", businessOp.BaseEvent, access.Operator)
	}

	if businessOp.BusinessType == ActionOfHostConnected {
		var conf *HostConnected

		bytesData, _ := json.Marshal(access.Request)

		_ = json.Unmarshal(bytesData, &conf)
		access.Event = conf.Event
	}

	if businessOp.BusinessType == ActionOfUser {
		access.LogType = "access"
		access.Response = map[string]interface{}{}
	}

	if businessOp.ParseResp {
		if businessOp.BusinessType == ActionOfClient {
			fileName := strings.Split(ctx.Writer.Header().Get("Content-Disposition"), "=")
			version := ctx.Writer.Header().Get("Client-Version")
			if len(fileName) > 1 {
				access.Payload = map[string]interface{}{
					"file_name": fileName[1],
					"version":   version,
				}
			}
		} else {
			payload := make(map[string]interface{})
			byteValue, err := json.Marshal(access.Response)
			if err == nil {
				if err = json.Unmarshal(byteValue, &payload); err == nil {
					access.Payload = payload
				}
			}

		}

	}

	//都处理为json字符串
	jsonPayload, _ := json.Marshal(access.Payload)
	access.Payload = string(jsonPayload)

	jsonResponse, _ := json.Marshal(access.Response)
	access.Response = string(jsonResponse)

	jsonReq, _ := json.Marshal(access.Request)
	access.Request = string(jsonReq)

	jsonHeader, _ := json.Marshal(access.Header)
	access.Header = string(jsonHeader)

}

func BuildData(ctx *gin.Context, access *AccessField) {
	//都处理为json字符串
	jsonPayload, _ := json.Marshal(access.Payload)
	access.Payload = string(jsonPayload)

	jsonResponse, _ := json.Marshal(access.Response)
	access.Response = string(jsonResponse)

	jsonReq, _ := json.Marshal(access.Request)
	access.Request = string(jsonReq)

	jsonHeader, _ := json.Marshal(access.Header)
	access.Header = string(jsonHeader)
}
