package helper

import (
	"bytes"
	"context"
	"crypto/aes"
	"crypto/cipher"
	"crypto/md5"
	"crypto/tls"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"math/rand"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

func GeneratorPassportSign(appid string, appSecret string, uuid string, time string) string {
	return Md5(fmt.Sprintf("%s%s%s%s", appid, appSecret, uuid, time))
}

func GetClientIp(ctx *gin.Context) string {
	clientIp := ctx.Request.Header.Get("X-Forwarded-For")
	for _, ip := range strings.Split(clientIp, ",") {
		ip = strings.TrimSpace(ip)
		if ip != "" && ip != "127.0.0.1" {
			clientIp = ip
			break
		}
	}

	if clientIp == "" {
		clientIp = ctx.Request.Header.Get("X-Real-IP")
	}

	if clientIp == "" {
		clientIp = ctx.ClientIP()
	}

	if clientIp == "::1" {
		clientIp = "127.0.0.1"
	}

	return clientIp
}

func Md5(str string) string {
	m := md5.New()
	m.Write([]byte(str))
	return fmt.Sprintf("%x", m.Sum(nil))
}

func RawUrlEncode(str string) string {
	return strings.Replace(url.QueryEscape(str), "+", "%20", -1)
}

func ParseTimeString(str string) (int64, error) {
	if str == "" {
		return 0, fmt.Errorf("time string is empty")
	}

	var ts int64 = 0
	numbers := make([]byte, 0)

	for i := 0; i < len(str); i++ {
		switch str[i] {
		case '0', '1', '2', '3', '4', '5', '6', '7', '8', '9':
			numbers = append(numbers, str[i])
		case 'd':
			value, _ := strconv.ParseInt(string(numbers), 10, 32)
			ts += int64(time.Hour*24) * value
			numbers = make([]byte, 0)
		case 'h':
			value, _ := strconv.ParseInt(string(numbers), 10, 32)
			ts += int64(time.Hour) * value
			numbers = make([]byte, 0)
		case 'm':
			value, _ := strconv.ParseInt(string(numbers), 10, 32)
			ts += int64(time.Minute) * value
			numbers = make([]byte, 0)
		case 's':
			value, _ := strconv.ParseInt(string(numbers), 10, 32)
			ts += int64(time.Second) * value
			numbers = make([]byte, 0)
		default:
			return 0, fmt.Errorf("invalid char `%c` found in time string", str[i])
		}
	}

	return ts / int64(time.Second), nil
}

func CurlRequest(apiUrl string, requestType string, data []byte, header map[string]string, InsecureSkipVerify bool) ([]byte, error) {
	buffer := bytes.NewBuffer(data)

	http.DefaultTransport.(*http.Transport).TLSClientConfig = &tls.Config{InsecureSkipVerify: InsecureSkipVerify}

	request, err := http.NewRequest(requestType, apiUrl, buffer)

	if err != nil {
		return []byte(""), err
	}

	if len(header) > 0 {
		for k, val := range header {
			request.Header.Add(k, val) //添加请求头
		}
	} else {
		if requestType != "GET" {
			request.Header.Set("Content-Type", "application/json;charset=UTF-8") //添加请求头
		}
	}

	//创建客户端
	client := http.Client{
		Timeout: 30 * time.Second,
	}

	resp, err := client.Do(request.WithContext(context.TODO())) //发送请求
	if err != nil {
		return []byte(""), err
	}

	defer resp.Body.Close()

	respBytes, err := io.ReadAll(resp.Body)
	return respBytes, err
}

// AesDecrypt 解密函数
func AesDecrypt(ciphertext string, key []byte) ([]byte, error) {
	ciphertextBytes, err := base64.StdEncoding.DecodeString(ciphertext)
	if err != nil {
		return nil, err
	}

	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}

	blockSize := block.BlockSize()
	if len(ciphertextBytes) < blockSize {
		return nil, fmt.Errorf("ciphertext too short")
	}

	iv := ciphertextBytes[:blockSize]
	ciphertextBytes = ciphertextBytes[blockSize:]

	if len(ciphertextBytes)%blockSize != 0 {
		return nil, fmt.Errorf("ciphertext is not a multiple of the block size")
	}

	mode := cipher.NewCBCDecrypter(block, iv)
	mode.CryptBlocks(ciphertextBytes, ciphertextBytes)

	// 去除填充
	return pkcs7UnPadding(ciphertextBytes)
}

// PKCS7 填充
func pkcs7Padding(data []byte, blockSize int) []byte {
	padding := blockSize - len(data)%blockSize
	padtext := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(data, padtext...)
}

// PKCS7 去除填充
func pkcs7UnPadding(data []byte) ([]byte, error) {
	length := len(data)
	unpadding := int(data[length-1])
	if unpadding > length {
		return nil, fmt.Errorf("unpadding size is invalid")
	}
	return data[:(length - unpadding)], nil
}

// CreateCaptcha 生成指定位数验证码
func CreateCaptcha(num int) (string, error) {
	str := "1"
	for i := 0; i < num; i++ {
		str += strconv.Itoa(0)
	}
	str10 := str
	int10, err := strconv.ParseInt(str10, 10, 64)

	if err != nil {
		return "", fmt.Errorf("生成验证码错误：%v", err)
	}

	j := int32(int10)
	return fmt.Sprintf("%0"+strconv.Itoa(num)+"v", rand.New(rand.NewSource(time.Now().UnixNano())).Int31n(j)), nil
}

func InArray(needle interface{}, hystack interface{}) bool {
	switch key := needle.(type) {
	case string:
		for _, item := range hystack.([]string) {
			if key == item {
				return true
			}
		}
	case int:
		for _, item := range hystack.([]int) {
			if key == item {
				return true
			}
		}
	case int64:
		for _, item := range hystack.([]int64) {
			if key == item {
				return true
			}
		}
	default:
		return false
	}
	return false
}

func JsonNotEscapeHTML(data any) (string, error) {
	var buf strings.Builder
	enc := json.NewEncoder(&buf)
	enc.SetEscapeHTML(false)

	if err := enc.Encode(data); err != nil {
		return "", err
	}

	return buf.String(), nil
}
func GetExcelTplPath(tplName string) string {
	tplPath := "exceltpl/" + tplName
	//if runtime.GOOS == "windows" {
	//	tplPath
	//} else {
	//
	//}
	return tplPath
}
