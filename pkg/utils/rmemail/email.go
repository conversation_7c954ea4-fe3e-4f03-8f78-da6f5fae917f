package rmemail

import (
	"gopkg.in/gomail.v2"
)

type RmEmailConfig struct {
	Port      int
	Host      string
	User      string
	Password  string
	AliasName string
}

type RmEmailAttach struct {
	FileName    string
	FileSetting gomail.FileSetting
}

type RmEmail struct {
	emailConfig RmEmailConfig
}

func NewRmEmail(emailConfig RmEmailConfig) *RmEmail {
	return &RmEmail{
		emailConfig: emailConfig,
	}
}

func (r *RmEmail) SendEmail(content, subject string, to, cc, bcc []string, attach ...RmEmailAttach) error {
	port := r.emailConfig.Port
	host := r.emailConfig.Host
	user := r.emailConfig.User
	password := r.emailConfig.Password
	nameAlias := r.emailConfig.AliasName

	m := gomail.NewMessage()
	m.SetAddressHeader("From", user, nameAlias)
	m.SetHeader("To", to...)
	if len(cc) > 0 {
		m.SetHeader("Cc", cc...)
	}
	if len(bcc) > 0 {
		m.SetHeader("Bcc", bcc...)
	}
	m.SetHeader("Subject", subject)

	m.SetBody("text/html", content)

	if len(attach) > 0 {
		for _, a := range attach {
			m.Attach(a.FileName, a.FileSetting)
		}
	}

	d := gomail.NewDialer(host, port, user, password)

	if err := d.DialAndSend(m); err != nil {
		return err
	}
	return nil
}
