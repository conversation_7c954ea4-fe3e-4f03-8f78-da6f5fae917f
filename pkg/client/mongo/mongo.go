package mongo

import (
	"context"
	"fmt"

	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

func NewMongodb(address, userName, password, database, mgoOptions, replicaSet string, direct bool) (*mongo.Database, error) {
	var err error
	uri := fmt.Sprintf("mongodb://%s:%s@%s/%s", userName, password, address, database)

	if mgoOptions != "" {
		uri += "?" + mgoOptions
	}

	opt := options.Client()
	opt = opt.SetDirect(direct)

	if replicaSet != "" {
		opt = opt.SetReplicaSet(replicaSet)
	}

	opt = opt.ApplyURI(uri)

	client, err := mongo.Connect(context.Background(), opt)

	if err != nil {
		return nil, fmt.Errorf("connect to mongodb failed, %v，连接配置：%+v，dbname: %v", err, address, database)
	}

	if err = client.Ping(context.Background(), nil); err != nil {
		return nil, fmt.Errorf("connect to mongodb failed, %v，连接配置：%+v，dbname: %v", err, address, database)
	}

	fmt.Printf("mongodb config:%+v，dbname: %v\n", address, database)

	return client.Database(database), nil
}
