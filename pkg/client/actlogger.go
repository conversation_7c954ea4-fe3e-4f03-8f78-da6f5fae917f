/**
* Author: <PERSON>
* Email: wudong<PERSON>@rongma.com
* Date: 2024/10/16
* Time: 15:46
* Software: GoLand
 */

package client

import (
	"io"
	"log"
	"os"
	"path/filepath"
	"sase-client-info/pkg/utils/helper"

	"strings"
	"time"
)

var defaultName = "console-proxy-log"

type ActLogger struct {
	isDebug   bool
	IndexName string
	log       *log.Logger
	logType   int //0终端和log文件都有，1只在终端有，2只写log文件
}

type ActOption func(act *ActLogger) *ActLogger

func NewActLogger(opts ...ActOption) *ActLogger {
	var logFile *os.File
	var multiWriter io.Writer
	act := &ActLogger{log: log.Default()}
	act.log.SetFlags(0)

	for _, opt := range opts {
		opt(act)
	}
	now := time.Now().Format("2006-01-02")
	logDir := getCurrentDirectory() + "/log/"
	_, err := os.Stat(logDir)
	if err != nil {
		_ = os.Mkdir(logDir, 0777)
	}
	file := logDir + defaultName + "_" + now + ".log"

	if act.logType == 0 || act.logType == 2 {
		logFile, err = os.OpenFile(file, os.O_RDWR|os.O_CREATE|os.O_APPEND, 0777)

		if err != nil {
			return act
		}
	}
	switch act.logType {
	case 0:
		multiWriter = io.MultiWriter(os.Stdout, logFile)
	case 1:
		multiWriter = io.MultiWriter(os.Stdout)
	case 2:
		multiWriter = io.MultiWriter(logFile)
	}
	act.log.SetOutput(multiWriter)

	return act
}
func WithIndexName(indexName string) ActOption {
	return func(act *ActLogger) *ActLogger {
		act.IndexName = indexName
		return act
	}
}

func WithLogType(logType int) ActOption {
	return func(act *ActLogger) *ActLogger {
		act.logType = logType
		return act
	}
}

func WithDebug(isDebug bool) ActOption {
	return func(act *ActLogger) *ActLogger {
		act.isDebug = isDebug
		return act
	}
}

func (a *ActLogger) Access(access *helper.AccessField) {
	if a.isDebug {
		go func() {
			if a.IndexName != "" {
				access.IndexName = a.IndexName
			}
			outJson, _ := helper.JsonNotEscapeHTML(access)
			a.log.Printf("%s", outJson)
		}()
	}
}

func getCurrentDirectory() string {
	dir, err := filepath.Abs(filepath.Dir(os.Args[0])) // 返回绝对路径 filepath.Dir(os.Args[0])去除最后一个元素的路径
	if err != nil {
		log.Fatal(err)
	}

	dir = strings.Replace(dir, "\\", "/", -1) // 将\替换成/
	dir = strings.Replace(dir, "../", "", -1)

	return dir
}
