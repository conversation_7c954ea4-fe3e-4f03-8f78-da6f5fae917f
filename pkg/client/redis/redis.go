package redis

import (
	"context"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"
)

func newRedis(address, password string, db int) (*redis.Client, error) {
	client := redis.NewClient(&redis.Options{
		Addr:     address,
		Password: password,
		DB:       db},
	)

	if _, err := client.Ping(context.Background()).Result(); err != nil {
		return nil, fmt.Errorf("connect to redis failed, %v", err)
	}

	return client, nil
}

func newRedisCluster(address []string, password string, db int) (*redis.ClusterClient, error) {
	client := redis.NewClusterClient(&redis.ClusterOptions{
		Addrs:        address,
		Password:     password,
		PoolSize:     40,
		MinIdleConns: 10,
		DialTimeout:  5 * time.Second,
	})

	if _, err := client.Ping(context.Background()).Result(); err != nil {
		return nil, fmt.Errorf("connect to redis cluster failed, %v", err)
	}

	return client, nil
}

func NewUniversalRedis(address []string, password string, db, connectionMode int) (redis.UniversalClient, error) {
	var cli redis.UniversalClient
	var err error
	if connectionMode == 0 { // 单机模式
		cli, err = newRedis(address[0], password, db)
	} else { // 集群模式
		cli, err = newRedisCluster(address, password, db)
	}
	return cli, err
}
