package jwt

import (
	"errors"
	"fmt"
	"log"
	"strconv"
	"time"

	"github.com/golang-jwt/jwt/v5"
	rmCommonInterfaces "rm.git/client_api/rm_common_libs.git/v2/interfaces"
)

// 定义错误
var (
	ErrInvalidToken = errors.New("invalid token")
	ErrExpiredToken = errors.New("expired token")
	ErrDeletedToken = errors.New("deleted token")
)

func Init(redisPrefix, signKey, ExpiredTime string, redisCli rmCommonInterfaces.RedisClient) (*JWTAuth, error) {
	if signKey == "" {
		log.Fatal("jwt sign error")
	}

	if ExpiredTime == "" {
		log.Fatal("jwt expired_time error")
	}

	if redisPrefix == "" {
		log.Fatal("jwt prefix error")
	}

	var opts []Option

	expiredTime, err := parseTimeString(ExpiredTime)
	if err != nil {
		log.Fatalf("jwt expired_time error: %v", err)
	}

	opts = append(opts, SetExpired(int(expiredTime)))
	opts = append(opts, SetSigningKey([]byte(signKey)))
	opts = append(opts, SetKeyfunc(func(t *jwt.Token) (interface{}, error) {
		if _, ok := t.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, ErrInvalidToken
		}
		return []byte(signKey), nil
	}))

	method := jwt.SigningMethodHS256
	opts = append(opts, SetSigningMethod(method))

	auth := newJwt(NewRedisStore(redisCli, redisPrefix), opts...)

	return auth, nil
}

func parseTimeString(str string) (int64, error) {
	if str == "" {
		return 0, fmt.Errorf("time string is empty")
	}

	var ts int64 = 0
	numbers := make([]byte, 0)

	for i := 0; i < len(str); i++ {
		switch str[i] {
		case '0', '1', '2', '3', '4', '5', '6', '7', '8', '9':
			numbers = append(numbers, str[i])
		case 'd':
			value, _ := strconv.ParseInt(string(numbers), 10, 32)
			ts += int64(time.Hour*24) * value
			numbers = make([]byte, 0)
		case 'h':
			value, _ := strconv.ParseInt(string(numbers), 10, 32)
			ts += int64(time.Hour) * value
			numbers = make([]byte, 0)
		case 'm':
			value, _ := strconv.ParseInt(string(numbers), 10, 32)
			ts += int64(time.Minute) * value
			numbers = make([]byte, 0)
		case 's':
			value, _ := strconv.ParseInt(string(numbers), 10, 32)
			ts += int64(time.Second) * value
			numbers = make([]byte, 0)
		default:
			return 0, fmt.Errorf("invalid char `%c` found in time string", str[i])
		}
	}

	return ts / int64(time.Second), nil
}
