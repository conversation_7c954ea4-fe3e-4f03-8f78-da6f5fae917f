package logger

import (
	"fmt"
	"io"
	"log"
	"os"
	"path/filepath"
	"runtime"
	"strings"
	"time"
)

// 日志级别
const (
	DEBUG = iota
	INFO
	WARN
	ERROR
	FATAL
)

var (
	// 日志级别名称
	levelNames = []string{
		"DEBUG",
		"INFO",
		"WARN",
		"ERROR",
		"FATAL",
	}

	// 日志级别颜色（终端显示）
	levelColors = []string{
		"\033[36m", // 青色 - DEBUG
		"\033[32m", // 绿色 - INFO
		"\033[33m", // 黄色 - WARN
		"\033[31m", // 红色 - ERROR
		"\033[35m", // 紫色 - FATAL
	}

	// 颜色重置
	colorReset = "\033[0m"

	// 全局日志记录器实例
	instance *Logger
)

// Logger 日志记录器
type Logger struct {
	level       int
	logFile     *os.File
	multiWriter io.Writer
}

// Init 初始化日志记录器
func Init(cfglevel, cfgpath string) error {

	// 获取日志级别
	level := getLevelFromString(cfglevel)

	// 创建日志目录
	if err := os.MkdirAll(cfgpath, 0755); err != nil {
		return fmt.Errorf("创建日志目录失败: %w", err)
	}

	// 创建日志文件
	filename := time.Now().Format("2006-01-02") + ".log"
	logPath := filepath.Join(cfgpath, filename)
	file, err := os.OpenFile(logPath, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
	if err != nil {
		return fmt.Errorf("创建日志文件失败: %w", err)
	}

	// 创建多输出写入器（同时写入文件和标准输出）
	multiWriter := io.MultiWriter(file, os.Stdout)

	// 设置标准日志输出
	log.SetOutput(multiWriter)

	// 初始化日志记录器实例
	instance = &Logger{
		level:       level,
		logFile:     file,
		multiWriter: multiWriter,
	}

	return nil
}

// Reopen 重新打开日志文件（用于日志轮转）
func Reopen(cfglevel, cfgpath string) error {
	if instance == nil {
		return fmt.Errorf("日志记录器未初始化")
	}

	// 关闭现有日志文件
	if instance.logFile != nil {
		instance.logFile.Close()
	}

	// 创建新的日志文件
	filename := time.Now().Format("2006-01-02") + ".log"
	logPath := filepath.Join(cfgpath, filename)
	file, err := os.OpenFile(logPath, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
	if err != nil {
		return fmt.Errorf("重新打开日志文件失败: %w", err)
	}

	// 更新多输出写入器
	instance.logFile = file
	instance.multiWriter = io.MultiWriter(file, os.Stdout)

	// 更新标准日志输出
	log.SetOutput(instance.multiWriter)

	return nil
}

// Close 关闭日志文件
func Close() {
	if instance != nil && instance.logFile != nil {
		instance.logFile.Close()
	}
}

// Debugf 记录调试级别日志
func Debugf(format string, args ...interface{}) {
	if instance == nil || instance.level > DEBUG {
		return
	}
	logWithCallerInfo(DEBUG, format, args...)
}

// Debug 记录调试级别日志
func Debug(args ...interface{}) {
	if instance == nil || instance.level > DEBUG {
		return
	}
	logWithCallerInfo(DEBUG, "", args...)
}

// Info 记录信息级别日志
func Info(args ...interface{}) {
	if instance == nil || instance.level > INFO {
		return
	}
	logWithCallerInfo(INFO, "", args...)
}

// Infof 记录信息级别日志
func Infof(format string, args ...interface{}) {
	if instance == nil || instance.level > INFO {
		return
	}
	logWithCallerInfo(INFO, format, args...)
}

// Warnf 记录警告级别日志
func Warnf(format string, args ...interface{}) {
	if instance == nil || instance.level > WARN {
		return
	}
	logWithCallerInfo(WARN, format, args...)
}

// Warn 记录警告级别日志
func Warn(args ...interface{}) {
	if instance == nil || instance.level > WARN {
		return
	}
	logWithCallerInfo(WARN, "", args...)
}

// Error 记录错误级别日志
func Error(args ...interface{}) {
	if instance == nil || instance.level > ERROR {
		return
	}
	logWithCallerInfo(ERROR, "", args...)
}

// Errorf 记录错误级别日志
func Errorf(format string, args ...interface{}) {
	if instance == nil || instance.level > ERROR {
		return
	}
	logWithCallerInfo(ERROR, format, args...)
}

// Fatal 记录致命级别日志并退出程序
func Fatal(args ...interface{}) {
	if instance == nil || instance.level > FATAL {
		os.Exit(1)
	}
	logWithCallerInfo(FATAL, "", args...)
	os.Exit(1)
}

// Fatalf 记录致命级别日志并退出程序
func Fatalf(format string, args ...interface{}) {
	if instance == nil || instance.level > FATAL {
		os.Exit(1)
	}
	logWithCallerInfo(FATAL, format, args...)
	os.Exit(1)
}

// WithError 记录带有错误信息的日志
func WithError(err error, level int, msg string) {
	if err == nil {
		return
	}

	errMsg := fmt.Sprintf("%s: %v", msg, err)
	switch level {
	case DEBUG:
		Debug(errMsg)
	case INFO:
		Info(errMsg)
	case WARN:
		Warn(errMsg)
	case ERROR:
		Error(errMsg)
	case FATAL:
		Fatal(errMsg)
	default:
		Error(errMsg)
	}
}

// 获取调用者信息
func getCallerInfo() (string, int, string) {
	// 跳过前3个调用栈帧（runtime.Callers, getCallerInfo, logWithCallerInfo）
	pc, file, line, ok := runtime.Caller(3)
	if !ok {
		return "unknown", 0, "unknown"
	}

	// 获取函数名
	funcName := runtime.FuncForPC(pc).Name()
	// 提取短函数名（去掉包路径）
	lastSlash := strings.LastIndexByte(funcName, '/')
	if lastSlash >= 0 {
		funcName = funcName[lastSlash+1:]
	}
	lastDot := strings.LastIndexByte(funcName, '.')
	if lastDot >= 0 {
		funcName = funcName[lastDot+1:]
	}

	// 提取短文件名（去掉目录路径）
	lastSlash = strings.LastIndexByte(file, '/')
	if runtime.GOOS == "windows" {
		// 在Windows上处理反斜杠
		lastBackslash := strings.LastIndexByte(file, '\\')
		if lastBackslash > lastSlash {
			lastSlash = lastBackslash
		}
	}
	if lastSlash >= 0 {
		file = file[lastSlash+1:]
	}

	return file, line, funcName
}

// 记录带有调用者信息的日志
func logWithCallerInfo(level int, format string, args ...interface{}) {
	if instance == nil || level < instance.level {
		return
	}

	// 获取当前时间
	now := time.Now().Format("2006-01-02 15:04:05.000")

	// 获取调用者信息
	file, line, funcName := getCallerInfo()
	callerInfo := fmt.Sprintf("%s:%d[%s]", file, line, funcName)

	var msg string
	if format == "" {
		msg = fmt.Sprint(args...)
	} else {
		msg = fmt.Sprintf(format, args...)
	}

	// 构建日志条目
	levelName := levelNames[level]

	// 在终端中使用颜色，在文件中不使用颜色
	colorStart := ""
	colorEnd := ""
	if isTerminal() {
		colorStart = levelColors[level]
		colorEnd = colorReset
	}

	// 格式化完整日志条目
	logEntry := fmt.Sprintf("%s %s%s%s [%s] %s\n",
		now, colorStart, levelName, colorEnd, callerInfo, msg)

	// 直接写入多输出写入器
	fmt.Fprint(instance.multiWriter, logEntry)
}

// 获取日志级别
func getLevelFromString(levelStr string) int {
	switch strings.ToUpper(levelStr) {
	case "DEBUG":
		return DEBUG
	case "INFO":
		return INFO
	case "WARN":
		return WARN
	case "ERROR":
		return ERROR
	case "FATAL":
		return FATAL
	default:
		return INFO
	}
}

// 判断当前输出是否为终端
func isTerminal() bool {
	fileInfo, err := os.Stdout.Stat()
	if err != nil {
		return false
	}

	// 检查是否是字符设备
	return (fileInfo.Mode() & os.ModeCharDevice) != 0
}
