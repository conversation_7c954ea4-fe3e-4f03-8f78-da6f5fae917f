package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"sase-client-info/internal/application"
	"sase-client-info/internal/config"
	"sase-client-info/internal/model"
	"sase-client-info/internal/resource"
	"sase-client-info/pkg/logger"

	"github.com/gin-gonic/gin"
)

func main() {
	// 加载配置
	if err := config.Init(); err != nil {
		log.Fatalf("配置初始化失败: %v", err)
	}

	cfg := config.Get()

	// 初始化日志
	if err := logger.Init(cfg.Log.Level, cfg.Log.Path); err != nil {
		log.Fatalf("日志初始化失败: %v", err)
	}
	defer logger.Close()

	// 记录启动信息
	logger.Info("服务正在启动...")

	// 初始化 资源
	resource.Init()

	// 初始化Model层
	model.Init()

	// 设置Gin模式
	gin.SetMode(cfg.Server.Mode)

	// 创建Gin引擎
	r := gin.Default()

	// 注册路由
	application.RegisterRoutes(r)

	// 创建HTTP服务器
	srv := &http.Server{
		Addr:    fmt.Sprintf(":%d", cfg.Server.Port),
		Handler: r,
	}

	// 启动HTTP服务器
	go func() {
		logger.Infof("HTTP服务器监听端口: %d", cfg.Server.Port)
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.Fatalf("HTTP服务器启动失败: %v", err)
		}
	}()

	logger.Infof("服务器已启动，监听端口: %d, 模式: %s", cfg.Server.Port, cfg.Server.Mode)

	// 优雅关闭
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	logger.Info("正在关闭服务器...")

	// 关闭资源
	resource.Close()

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	if err := srv.Shutdown(ctx); err != nil {
		logger.Errorf("服务器关闭失败: %v", err)
	}

	logger.Info("服务器已关闭")
}
